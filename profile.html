<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 0 0 32px 32px;
            position: relative;
            overflow: hidden;
        }

        .profile-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .vip-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #8b5a00;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .health-chart {
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            padding: 20px;
        }
        
        .menu-item {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.2s ease;
        }
        
        .menu-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-confirmed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-completed {
            background: #e0e7ff;
            color: #3730a3;
        }
        
        .order-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border-left: 4px solid #3b82f6;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50">
    <!-- 个人信息头部 -->
    <div class="profile-header px-6 py-8 text-white relative">
        <div class="flex items-center mb-6 relative z-10">
            <div class="relative">
                <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-5 backdrop-blur-sm">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face"
                         alt="用户头像" class="w-18 h-18 rounded-full object-cover">
                </div>
                <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                    <i class="fas fa-check text-white text-xs"></i>
                </div>
            </div>
            <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                    <h2 class="text-2xl font-bold">张先生</h2>
                    <span class="vip-badge">
                        <i class="fas fa-crown mr-1"></i>
                        VIP钻石
                    </span>
                </div>
                <p class="text-white text-opacity-90 text-sm mb-1">会员编号: VIP20240001</p>
                <div class="flex items-center text-sm text-white text-opacity-80">
                    <i class="fas fa-calendar-check mr-1"></i>
                    <span>已完成2次体检 · 下次体检: 2024年6月</span>
                </div>
            </div>
            <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <i class="fas fa-cog text-white"></i>
            </button>
        </div>

        <!-- 会员等级进度 -->
        <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl p-4 mb-6 relative z-10">
            <div class="flex items-center justify-between mb-3">
                <span class="text-sm font-medium">会员等级进度</span>
                <span class="text-sm text-white text-opacity-80">距离黑钻还需 2,000 积分</span>
            </div>
            <div class="w-full bg-white bg-opacity-20 rounded-full h-2 mb-2">
                <div class="bg-yellow-400 h-2 rounded-full" style="width: 75%"></div>
            </div>
            <div class="flex justify-between text-xs text-white text-opacity-80">
                <span>钻石会员</span>
                <span>黑钻会员</span>
            </div>
        </div>

        <!-- 快捷统计 -->
        <div class="grid grid-cols-4 gap-3 relative z-10">
            <div class="text-center bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-3">
                <div class="text-2xl font-bold mb-1">8,500</div>
                <div class="text-xs text-white text-opacity-80">积分余额</div>
            </div>
            <div class="text-center bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-3">
                <div class="text-2xl font-bold mb-1">3</div>
                <div class="text-xs text-white text-opacity-80">预约记录</div>
            </div>
            <div class="text-center bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-3">
                <div class="text-2xl font-bold mb-1">2</div>
                <div class="text-xs text-white text-opacity-80">已完成</div>
            </div>
            <div class="text-center bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-3">
                <div class="text-2xl font-bold mb-1">98</div>
                <div class="text-xs text-white text-opacity-80">健康分</div>
            </div>
        </div>
    </div>

    <div class="px-6 py-6">
        <!-- 健康数据可视化 -->
        <div class="health-chart mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-800">健康趋势分析</h3>
                <button class="text-blue-500 text-sm font-medium">
                    查看详细报告 <i class="fas fa-chevron-right ml-1"></i>
                </button>
            </div>
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center p-3 bg-green-50 rounded-xl">
                    <div class="text-2xl font-bold text-green-600 mb-1">98</div>
                    <div class="text-sm text-gray-600">健康评分</div>
                    <div class="text-xs text-green-600">↑ 比上次提升5分</div>
                </div>
                <div class="text-center p-3 bg-blue-50 rounded-xl">
                    <div class="text-2xl font-bold text-blue-600 mb-1">正常</div>
                    <div class="text-sm text-gray-600">体检结果</div>
                    <div class="text-xs text-blue-600">各项指标良好</div>
                </div>
            </div>
            <div class="h-32 bg-gray-50 rounded-xl flex items-center justify-center">
                <canvas id="healthChart" width="300" height="120"></canvas>
            </div>
        </div>

        <!-- 我的订单 -->
        <div class="mb-6">
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-xl font-bold text-gray-800">我的订单</h3>
                <button class="text-blue-500 text-sm font-medium flex items-center">
                    查看全部 <i class="fas fa-chevron-right ml-1"></i>
                </button>
            </div>

            <div class="space-y-4">
                <div class="order-card p-5 relative overflow-hidden">
                    <div class="absolute top-4 right-4">
                        <span class="status-badge status-pending">待体检</span>
                    </div>
                    <div class="flex items-center mb-4">
                        <img src="https://images.unsplash.com/photo-**********-5c350d0d3c56?w=60&h=60&fit=crop"
                             alt="体检项目" class="w-15 h-15 rounded-xl object-cover mr-4">
                        <div class="flex-1">
                            <h4 class="font-bold text-gray-800 text-lg">PET-CT全身精密体检</h4>
                            <p class="text-sm text-gray-600 mb-1">订单号：TJ202401120001</p>
                            <div class="flex items-center text-xs text-gray-500">
                                <i class="fas fa-map-marker-alt mr-1 text-blue-500"></i>
                                <span>东京大学医院</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <div>
                            <div class="text-sm text-gray-600">体检时间</div>
                            <div class="font-semibold text-gray-800">2024年1月12日 下午</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">费用</div>
                            <div class="font-bold text-blue-600 text-lg">¥28,800</div>
                        </div>
                    </div>
                    <div class="mt-4 flex gap-3">
                        <button class="flex-1 bg-blue-500 text-white py-2 rounded-lg text-sm font-medium">
                            查看详情
                        </button>
                        <button class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-lg text-sm font-medium">
                            修改预约
                        </button>
                    </div>
                </div>

                <div class="order-card p-5 relative overflow-hidden" style="border-left-color: #10b981;">
                    <div class="absolute top-4 right-4">
                        <span class="status-badge status-completed">已完成</span>
                    </div>
                    <div class="flex items-center mb-4">
                        <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=60&h=60&fit=crop"
                             alt="体检项目" class="w-15 h-15 rounded-xl object-cover mr-4">
                        <div class="flex-1">
                            <h4 class="font-bold text-gray-800 text-lg">高端女性专项体检</h4>
                            <p class="text-sm text-gray-600 mb-1">订单号：TJ202312150002</p>
                            <div class="flex items-center text-xs text-gray-500">
                                <i class="fas fa-map-marker-alt mr-1 text-green-500"></i>
                                <span>大阪妇女医院</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <div>
                            <div class="text-sm text-gray-600">完成时间</div>
                            <div class="font-semibold text-gray-800">2023年12月15日</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">费用</div>
                            <div class="font-bold text-green-600 text-lg">¥18,800</div>
                        </div>
                    </div>
                    <div class="mt-4 flex gap-3">
                        <button class="flex-1 bg-green-500 text-white py-2 rounded-lg text-sm font-medium">
                            查看报告
                        </button>
                        <button class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-lg text-sm font-medium">
                            再次预约
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 积分系统 -->
        <div class="menu-item p-5 mb-6 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-coins text-white text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-bold text-gray-800 text-lg">积分商城</h4>
                        <p class="text-sm text-gray-600">当前积分: 8,500</p>
                    </div>
                </div>
                <button class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    兑换奖品
                </button>
            </div>
            <div class="grid grid-cols-3 gap-3">
                <div class="text-center bg-white rounded-lg p-3">
                    <div class="text-lg font-bold text-yellow-600">+500</div>
                    <div class="text-xs text-gray-600">完成体检</div>
                </div>
                <div class="text-center bg-white rounded-lg p-3">
                    <div class="text-lg font-bold text-green-600">+100</div>
                    <div class="text-xs text-gray-600">分享好友</div>
                </div>
                <div class="text-center bg-white rounded-lg p-3">
                    <div class="text-lg font-bold text-blue-600">+50</div>
                    <div class="text-xs text-gray-600">每日签到</div>
                </div>
            </div>
        </div>

        <!-- VIP会员权益 -->
        <div class="menu-item p-5 mb-6 bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-crown text-white text-xl"></i>
                </div>
                <div>
                    <h4 class="font-bold text-gray-800 text-lg">VIP钻石会员权益</h4>
                    <p class="text-sm text-gray-600">专享特权 · 尊贵体验</p>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-3">
                <div class="flex items-center bg-white rounded-lg p-3">
                    <i class="fas fa-percentage text-purple-500 mr-2"></i>
                    <span class="text-sm font-medium">9.5折优惠</span>
                </div>
                <div class="flex items-center bg-white rounded-lg p-3">
                    <i class="fas fa-shipping-fast text-blue-500 mr-2"></i>
                    <span class="text-sm font-medium">优先预约</span>
                </div>
                <div class="flex items-center bg-white rounded-lg p-3">
                    <i class="fas fa-user-md text-green-500 mr-2"></i>
                    <span class="text-sm font-medium">专家咨询</span>
                </div>
                <div class="flex items-center bg-white rounded-lg p-3">
                    <i class="fas fa-gift text-orange-500 mr-2"></i>
                    <span class="text-sm font-medium">生日礼品</span>
                </div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="menu-item p-5 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                    <i class="fas fa-file-medical-alt text-white text-xl"></i>
                </div>
                <h4 class="font-semibold text-gray-800 mb-1">体检报告</h4>
                <p class="text-xs text-gray-600">查看历史报告</p>
                <div class="mt-2">
                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">2份新报告</span>
                </div>
            </div>

            <div class="menu-item p-5 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                    <i class="fas fa-calendar-check text-white text-xl"></i>
                </div>
                <h4 class="font-semibold text-gray-800 mb-1">预约管理</h4>
                <p class="text-xs text-gray-600">管理我的预约</p>
                <div class="mt-2">
                    <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">1个待确认</span>
                </div>
            </div>

            <div class="menu-item p-5 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                    <i class="fas fa-video text-white text-xl"></i>
                </div>
                <h4 class="font-semibold text-gray-800 mb-1">视频咨询</h4>
                <p class="text-xs text-gray-600">在线专家咨询</p>
                <div class="mt-2">
                    <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">5位在线</span>
                </div>
            </div>

            <div class="menu-item p-5 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                    <i class="fas fa-gift text-white text-xl"></i>
                </div>
                <h4 class="font-semibold text-gray-800 mb-1">优惠券</h4>
                <p class="text-xs text-gray-600">我的优惠券</p>
                <div class="mt-2">
                    <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">3张可用</span>
                </div>
            </div>
        </div>

        <!-- 健康档案 -->
        <div class="menu-item p-4 mb-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-heartbeat text-red-500"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-800">健康档案</h4>
                        <p class="text-sm text-gray-600">管理个人健康信息</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 设置菜单 -->
        <div class="space-y-1">
            <div class="menu-item p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-user text-gray-500 mr-3"></i>
                        <span class="text-gray-800">个人信息</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <div class="menu-item p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-bell text-gray-500 mr-3"></i>
                        <span class="text-gray-800">消息通知</span>
                    </div>
                    <div class="flex items-center">
                        <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div class="menu-item p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-shield-alt text-gray-500 mr-3"></i>
                        <span class="text-gray-800">隐私设置</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <div class="menu-item p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-question-circle text-gray-500 mr-3"></i>
                        <span class="text-gray-800">帮助中心</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <div class="menu-item p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-gray-500 mr-3"></i>
                        <span class="text-gray-800">关于我们</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 退出登录 -->
        <div class="mt-6">
            <button class="w-full bg-red-50 text-red-600 py-3 rounded-lg font-medium border border-red-200">
                退出登录
            </button>
        </div>
    </div>

    <script>
        // 健康数据图表
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('healthChart').getContext('2d');

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '健康评分',
                        data: [85, 88, 92, 89, 95, 98],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#3b82f6',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 80,
                            max: 100,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });
        });

        // 菜单项点击效果
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                // 添加点击反馈
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 100);
            });
        });

        // 积分数字动画
        function animateNumber(element, targetValue, duration = 1000) {
            const startValue = 0;
            const startTime = performance.now();

            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
                element.textContent = currentValue.toLocaleString();

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }

            requestAnimationFrame(updateNumber);
        }

        // 页面加载时启动动画
        window.addEventListener('load', function() {
            // 积分动画
            const pointsElement = document.querySelector('.profile-header .text-2xl');
            if (pointsElement && pointsElement.textContent === '8,500') {
                pointsElement.textContent = '0';
                setTimeout(() => {
                    animateNumber(pointsElement, 8500, 2000);
                }, 500);
            }

            // 健康评分动画
            const healthScoreElement = document.querySelector('.health-chart .text-green-600');
            if (healthScoreElement && healthScoreElement.textContent === '98') {
                healthScoreElement.textContent = '0';
                setTimeout(() => {
                    animateNumber(healthScoreElement, 98, 1500);
                }, 1000);
            }

            // VIP徽章动画
            const vipBadge = document.querySelector('.vip-badge');
            if (vipBadge) {
                vipBadge.style.animation = 'shimmer 2s infinite';
            }
        });

        // 会员等级进度条动画
        function animateProgressBar() {
            const progressBar = document.querySelector('.bg-yellow-400');
            if (progressBar) {
                progressBar.style.width = '0%';
                setTimeout(() => {
                    progressBar.style.transition = 'width 2s ease-out';
                    progressBar.style.width = '75%';
                }, 500);
            }
        }

        // 启动进度条动画
        setTimeout(animateProgressBar, 1500);

        // 添加悬停效果
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
            });
        });

        // 订单卡片交互
        document.querySelectorAll('.order-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 12px 30px rgba(0,0,0,0.12)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
            });
        });
    </script>
</body>
</html>
