<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日本医疗健康体检小程序原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 尺寸配置 */
        .phone-container {
            width: 393px;
            height: 852px;
            margin: 20px auto;
            background: #000;
            border-radius: 47px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 39px;
            overflow: hidden;
            position: relative;
        }
        
        /* iOS 状态栏 */
        .status-bar {
            height: 47px;
            background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        
        /* 内容区域 */
        .content-area {
            height: calc(100% - 47px - 83px);
            overflow-y: auto;
        }
        
        /* 底部导航栏 */
        .bottom-nav {
            height: 83px;
            background: #fff;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #6b7280;
            font-size: 10px;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .nav-item.active {
            color: #3b82f6;
        }
        
        .nav-item i {
            font-size: 20px;
        }
        
        /* 页面iframe样式 */
        .page-iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: none;
        }
        
        .page-iframe.active {
            display: block;
        }
        
        /* 自定义滚动条 */
        .content-area::-webkit-scrollbar {
            width: 2px;
        }
        
        .content-area::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .content-area::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 1px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS 状态栏 -->
            <div class="status-bar">
                <div class="flex items-center gap-1">
                    <span>9:41</span>
                </div>
                <div class="flex items-center gap-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-three-quarters text-xs"></i>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-area">
                <iframe src="home.html" class="page-iframe active" id="home-frame"></iframe>
                <iframe src="packages.html" class="page-iframe" id="packages-frame"></iframe>
                <iframe src="booking.html" class="page-iframe" id="booking-frame"></iframe>
                <iframe src="profile.html" class="page-iframe" id="profile-frame"></iframe>
                <iframe src="consultation.html" class="page-iframe" id="consultation-frame"></iframe>
            </div>
            
            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <div class="nav-item active" onclick="switchPage('home')">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item" onclick="switchPage('packages')">
                    <i class="fas fa-heartbeat"></i>
                    <span>体检套餐</span>
                </div>
                <div class="nav-item" onclick="switchPage('booking')">
                    <i class="fas fa-calendar-check"></i>
                    <span>预约</span>
                </div>
                <div class="nav-item" onclick="switchPage('consultation')">
                    <i class="fas fa-user-md"></i>
                    <span>咨询</span>
                </div>
                <div class="nav-item" onclick="switchPage('profile')">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchPage(page) {
            // 隐藏所有页面
            document.querySelectorAll('.page-iframe').forEach(iframe => {
                iframe.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(page + '-frame').classList.add('active');
            
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
        }
    </script>
</body>
</html>
