<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日本医疗健康体检 - 高端精密体检服务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* iPhone 15 Pro Max 尺寸配置 */
        .phone-container {
            width: 430px;
            height: 932px;
            margin: 20px auto;
            background: linear-gradient(145deg, #1a1a1a, #000);
            border-radius: 55px;
            padding: 8px;
            box-shadow:
                0 0 50px rgba(0,0,0,0.5),
                inset 0 1px 0 rgba(255,255,255,0.1),
                inset 0 -1px 0 rgba(0,0,0,0.5);
            position: relative;
        }

        .phone-container::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 30px;
            background: #000;
            border-radius: 15px;
            z-index: 10;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, #fafbfc 0%, #f8f9fa 100%);
            border-radius: 47px;
            overflow: hidden;
            position: relative;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        /* iOS 状态栏 */
        .status-bar {
            height: 54px;
            background: linear-gradient(180deg, rgba(248,249,250,0.95) 0%, rgba(255,255,255,0.95) 100%);
            backdrop-filter: blur(20px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 15px;
            font-weight: 600;
            color: #000;
            position: relative;
            z-index: 100;
        }

        /* 内容区域 */
        .content-area {
            height: calc(100% - 54px - 100px);
            overflow-y: auto;
            position: relative;
        }

        /* 底部导航栏 */
        .bottom-nav {
            height: 100px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(229,231,235,0.3);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 34px;
            position: relative;
            z-index: 100;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            color: #6b7280;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 8px 12px;
            border-radius: 16px;
            position: relative;
        }

        .nav-item.active {
            color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            top: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 3px;
            background: #3b82f6;
            border-radius: 2px;
        }

        .nav-item i {
            font-size: 22px;
            transition: transform 0.3s ease;
        }

        .nav-item.active i {
            transform: scale(1.1);
        }

        /* 页面iframe样式 */
        .page-iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .page-iframe.active {
            display: block;
            opacity: 1;
        }

        /* 自定义滚动条 */
        .content-area::-webkit-scrollbar {
            width: 3px;
        }

        .content-area::-webkit-scrollbar-track {
            background: transparent;
        }

        .content-area::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #e5e7eb, #d1d5db);
            border-radius: 2px;
        }

        /* 高端动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* 毛玻璃效果 */
        .glass-effect {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 高端阴影 */
        .premium-shadow {
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS 状态栏 -->
            <div class="status-bar">
                <div class="flex items-center gap-1">
                    <span class="font-semibold">9:41</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="flex items-center gap-1">
                        <div class="flex space-x-1">
                            <div class="w-1 h-3 bg-black rounded-full"></div>
                            <div class="w-1 h-4 bg-black rounded-full"></div>
                            <div class="w-1 h-2 bg-black rounded-full"></div>
                            <div class="w-1 h-4 bg-black rounded-full"></div>
                        </div>
                    </div>
                    <i class="fas fa-wifi text-sm"></i>
                    <div class="flex items-center">
                        <div class="w-6 h-3 border border-black rounded-sm relative">
                            <div class="w-4 h-2 bg-black rounded-sm absolute top-0.5 left-0.5"></div>
                        </div>
                        <div class="w-0.5 h-1 bg-black rounded-full ml-0.5"></div>
                    </div>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-area">
                <iframe src="home.html" class="page-iframe active" id="home-frame"></iframe>
                <iframe src="packages.html" class="page-iframe" id="packages-frame"></iframe>
                <iframe src="booking.html" class="page-iframe" id="booking-frame"></iframe>
                <iframe src="profile.html" class="page-iframe" id="profile-frame"></iframe>
                <iframe src="consultation.html" class="page-iframe" id="consultation-frame"></iframe>
            </div>
            
            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <div class="nav-item active" onclick="switchPage('home')">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item" onclick="switchPage('packages')">
                    <i class="fas fa-heartbeat"></i>
                    <span>体检套餐</span>
                </div>
                <div class="nav-item" onclick="switchPage('booking')">
                    <i class="fas fa-calendar-check"></i>
                    <span>预约</span>
                </div>
                <div class="nav-item" onclick="switchPage('consultation')">
                    <i class="fas fa-user-md"></i>
                    <span>咨询</span>
                </div>
                <div class="nav-item" onclick="switchPage('profile')">
                    <i class="fas fa-user-circle"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchPage(page) {
            // 隐藏所有页面
            document.querySelectorAll('.page-iframe').forEach(iframe => {
                iframe.classList.remove('active');
            });

            // 显示目标页面
            setTimeout(() => {
                document.getElementById(page + '-frame').classList.add('active');
            }, 150);

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            // 添加触觉反馈效果
            if (navigator.vibrate) {
                navigator.vibrate(10);
            }
        }

        // 页面加载动画
        window.addEventListener('load', function() {
            document.querySelector('.phone-container').classList.add('animate-fade-in-up');
        });

        // 添加触摸反馈
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });

            item.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
