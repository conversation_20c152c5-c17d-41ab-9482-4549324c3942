<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咨询服务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .doctor-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        
        .doctor-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        
        .online-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            position: absolute;
            top: 4px;
            right: 4px;
            border: 2px solid white;
        }
        
        .chat-bubble {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            margin-bottom: 12px;
        }
        
        .chat-bubble.user {
            background: #3b82f6;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 6px;
        }
        
        .chat-bubble.doctor {
            background: #f3f4f6;
            color: #374151;
            margin-right: auto;
            border-bottom-left-radius: 6px;
        }
        
        .service-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.2s ease;
        }
        
        .service-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .faq-item {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.2s ease;
        }
        
        .faq-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .faq-item.active .faq-content {
            max-height: 200px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部标题栏 -->
    <div class="bg-white px-4 py-4 border-b border-gray-100">
        <h1 class="text-xl font-bold text-gray-800 text-center">咨询服务</h1>
    </div>

    <!-- 服务选项 -->
    <div class="px-4 py-4">
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="service-card p-4 text-center" onclick="showSection('doctors')">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-user-md text-blue-500 text-xl"></i>
                </div>
                <h3 class="font-medium text-gray-800 mb-1">专家咨询</h3>
                <p class="text-xs text-gray-600">在线医生问诊</p>
            </div>
            
            <div class="service-card p-4 text-center" onclick="showSection('customer-service')">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-headset text-green-500 text-xl"></i>
                </div>
                <h3 class="font-medium text-gray-800 mb-1">客服支持</h3>
                <p class="text-xs text-gray-600">24小时在线</p>
            </div>
        </div>

        <!-- 在线专家 -->
        <div id="doctors-section">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800">在线专家</h3>
                <span class="text-sm text-green-600 flex items-center">
                    <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                    5位专家在线
                </span>
            </div>
            
            <div class="space-y-4 mb-6">
                <div class="doctor-card p-4">
                    <div class="flex items-center">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=60&h=60&fit=crop&crop=face" 
                                 alt="田中医生" class="w-15 h-15 rounded-full object-cover">
                            <div class="online-indicator"></div>
                        </div>
                        <div class="ml-4 flex-1">
                            <h4 class="font-semibold text-gray-800">田中健一 医生</h4>
                            <p class="text-sm text-gray-600">肿瘤科主任医师 · 东京大学医院</p>
                            <div class="flex items-center mt-1">
                                <div class="flex text-yellow-400 text-xs">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="text-xs text-gray-500 ml-2">4.9分 (234评价)</span>
                            </div>
                        </div>
                        <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium" onclick="startChat('田中医生')">
                            咨询
                        </button>
                    </div>
                </div>
                
                <div class="doctor-card p-4">
                    <div class="flex items-center">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1594824475317-d3c2b8b7b3e0?w=60&h=60&fit=crop&crop=face" 
                                 alt="佐藤医生" class="w-15 h-15 rounded-full object-cover">
                            <div class="online-indicator"></div>
                        </div>
                        <div class="ml-4 flex-1">
                            <h4 class="font-semibold text-gray-800">佐藤美香 医生</h4>
                            <p class="text-sm text-gray-600">妇科专家 · 庆应义塾大学医院</p>
                            <div class="flex items-center mt-1">
                                <div class="flex text-yellow-400 text-xs">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="text-xs text-gray-500 ml-2">4.8分 (189评价)</span>
                            </div>
                        </div>
                        <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium" onclick="startChat('佐藤医生')">
                            咨询
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客服支持 -->
        <div id="customer-service-section" class="hidden">
            <h3 class="text-lg font-bold text-gray-800 mb-4">客服支持</h3>
            
            <div class="space-y-4 mb-6">
                <div class="service-card p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-comments text-green-500"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">在线客服</h4>
                                <p class="text-sm text-gray-600">24小时在线服务</p>
                            </div>
                        </div>
                        <button class="bg-green-500 text-white px-4 py-2 rounded-lg text-sm font-medium" onclick="startCustomerChat()">
                            开始对话
                        </button>
                    </div>
                </div>
                
                <div class="service-card p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-phone text-blue-500"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">电话咨询</h4>
                                <p class="text-sm text-gray-600">************</p>
                            </div>
                        </div>
                        <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
                            拨打
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 常见问题 -->
        <div>
            <h3 class="text-lg font-bold text-gray-800 mb-4">常见问题</h3>
            
            <div class="space-y-3">
                <div class="faq-item p-4" onclick="toggleFaq(this)">
                    <div class="flex justify-between items-center">
                        <h4 class="font-medium text-gray-800">日本体检需要提前多久预约？</h4>
                        <i class="fas fa-chevron-down text-gray-400 transition-transform"></i>
                    </div>
                    <div class="faq-content">
                        <p class="text-sm text-gray-600 mt-3">建议提前2-4周预约，特别是PET-CT等精密检查项目。我们会根据您的时间安排和医院档期为您推荐最佳的体检时间。</p>
                    </div>
                </div>
                
                <div class="faq-item p-4" onclick="toggleFaq(this)">
                    <div class="flex justify-between items-center">
                        <h4 class="font-medium text-gray-800">体检报告多久能出来？</h4>
                        <i class="fas fa-chevron-down text-gray-400 transition-transform"></i>
                    </div>
                    <div class="faq-content">
                        <p class="text-sm text-gray-600 mt-3">一般体检项目当天可出初步结果，完整报告需要3-5个工作日。PET-CT等特殊检查可能需要7-10个工作日。</p>
                    </div>
                </div>
                
                <div class="faq-item p-4" onclick="toggleFaq(this)">
                    <div class="flex justify-between items-center">
                        <h4 class="font-medium text-gray-800">费用包含哪些服务？</h4>
                        <i class="fas fa-chevron-down text-gray-400 transition-transform"></i>
                    </div>
                    <div class="faq-content">
                        <p class="text-sm text-gray-600 mt-3">费用包含所有体检项目、专业翻译、医院接送、报告解读等服务。不包含往返机票和住宿费用。</p>
                    </div>
                </div>
                
                <div class="faq-item p-4" onclick="toggleFaq(this)">
                    <div class="flex justify-between items-center">
                        <h4 class="font-medium text-gray-800">语言沟通有障碍怎么办？</h4>
                        <i class="fas fa-chevron-down text-gray-400 transition-transform"></i>
                    </div>
                    <div class="faq-content">
                        <p class="text-sm text-gray-600 mt-3">我们提供专业医疗翻译服务，全程陪同体检，确保医患沟通无障碍。所有报告都会提供中文翻译版本。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 聊天界面模态框 -->
    <div id="chat-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl h-4/5">
            <div class="flex items-center justify-between p-4 border-b">
                <div class="flex items-center">
                    <img id="chat-avatar" src="" alt="" class="w-10 h-10 rounded-full object-cover mr-3">
                    <div>
                        <h4 id="chat-name" class="font-medium text-gray-800"></h4>
                        <p class="text-sm text-green-600">在线</p>
                    </div>
                </div>
                <button onclick="closeChat()" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-times text-gray-600"></i>
                </button>
            </div>
            
            <div id="chat-messages" class="flex-1 p-4 overflow-y-auto" style="height: calc(100% - 140px);">
                <!-- 聊天消息将在这里显示 -->
            </div>
            
            <div class="p-4 border-t">
                <div class="flex items-center space-x-2">
                    <input type="text" id="message-input" placeholder="输入您的问题..." 
                           class="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button onclick="sendMessage()" class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(section) {
            document.getElementById('doctors-section').classList.add('hidden');
            document.getElementById('customer-service-section').classList.add('hidden');
            
            if (section === 'doctors') {
                document.getElementById('doctors-section').classList.remove('hidden');
            } else {
                document.getElementById('customer-service-section').classList.remove('hidden');
            }
        }
        
        function toggleFaq(element) {
            const isActive = element.classList.contains('active');
            
            // 关闭所有FAQ
            document.querySelectorAll('.faq-item').forEach(item => {
                item.classList.remove('active');
                item.querySelector('i').style.transform = 'rotate(0deg)';
            });
            
            // 如果当前不是激活状态，则激活
            if (!isActive) {
                element.classList.add('active');
                element.querySelector('i').style.transform = 'rotate(180deg)';
            }
        }
        
        function startChat(doctorName) {
            document.getElementById('chat-name').textContent = doctorName;
            document.getElementById('chat-avatar').src = 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=60&h=60&fit=crop&crop=face';
            
            // 清空聊天记录并添加欢迎消息
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.innerHTML = `
                <div class="chat-bubble doctor">
                    您好！我是${doctorName}，很高兴为您服务。请问有什么健康问题需要咨询吗？
                </div>
            `;
            
            document.getElementById('chat-modal').classList.remove('hidden');
        }
        
        function startCustomerChat() {
            document.getElementById('chat-name').textContent = '客服小助手';
            document.getElementById('chat-avatar').src = 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face';
            
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.innerHTML = `
                <div class="chat-bubble doctor">
                    您好！欢迎咨询日本医疗健康体检服务，我是您的专属客服。请问有什么可以帮助您的吗？
                </div>
            `;
            
            document.getElementById('chat-modal').classList.remove('hidden');
        }
        
        function closeChat() {
            document.getElementById('chat-modal').classList.add('hidden');
        }
        
        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (message) {
                const chatMessages = document.getElementById('chat-messages');
                
                // 添加用户消息
                const userBubble = document.createElement('div');
                userBubble.className = 'chat-bubble user';
                userBubble.textContent = message;
                chatMessages.appendChild(userBubble);
                
                // 模拟回复
                setTimeout(() => {
                    const doctorBubble = document.createElement('div');
                    doctorBubble.className = 'chat-bubble doctor';
                    doctorBubble.textContent = '感谢您的咨询，我正在为您分析问题，请稍等片刻...';
                    chatMessages.appendChild(doctorBubble);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }, 1000);
                
                input.value = '';
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }
        
        // 回车发送消息
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
