<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咨询服务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .doctor-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        
        .doctor-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        
        .online-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            position: absolute;
            top: 4px;
            right: 4px;
            border: 2px solid white;
        }
        
        .chat-bubble {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            margin-bottom: 12px;
        }
        
        .chat-bubble.user {
            background: #3b82f6;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 6px;
        }
        
        .chat-bubble.doctor {
            background: #f3f4f6;
            color: #374151;
            margin-right: auto;
            border-bottom-left-radius: 6px;
        }
        
        .service-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.2s ease;
        }
        
        .service-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .faq-item {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.2s ease;
        }
        
        .faq-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .faq-item.active .faq-content {
            max-height: 200px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50">
    <!-- 顶部标题栏 -->
    <div class="bg-white/80 backdrop-blur-lg px-6 py-5 border-b border-white/20">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">咨询服务</h1>
                <p class="text-sm text-gray-600 mt-1">专业医疗 · 贴心服务</p>
            </div>
            <div class="flex items-center gap-2">
                <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                <span class="text-sm text-green-600 font-medium">8位专家在线</span>
            </div>
        </div>
    </div>

    <!-- AI智能助手横幅 -->
    <div class="px-6 py-4">
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-5 text-white relative overflow-hidden">
            <div class="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16"></div>
            <div class="relative z-10">
                <div class="flex items-center mb-3">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-robot text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">AI健康助手</h3>
                        <p class="text-sm opacity-90">24小时智能问答</p>
                    </div>
                </div>
                <button class="bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-opacity-30 transition-all" onclick="startAIChat()">
                    开始咨询 <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 服务选项 -->
    <div class="px-6 py-4">
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="service-card p-5 text-center" onclick="showSection('doctors')">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                    <i class="fas fa-video text-white text-xl"></i>
                </div>
                <h3 class="font-semibold text-gray-800 mb-1">视频咨询</h3>
                <p class="text-xs text-gray-600">面对面专家问诊</p>
                <div class="mt-2">
                    <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">5位在线</span>
                </div>
            </div>

            <div class="service-card p-5 text-center" onclick="showSection('customer-service')">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                    <i class="fas fa-comments text-white text-xl"></i>
                </div>
                <h3 class="font-semibold text-gray-800 mb-1">在线客服</h3>
                <p class="text-xs text-gray-600">24小时人工服务</p>
                <div class="mt-2">
                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">即时响应</span>
                </div>
            </div>

            <div class="service-card p-5 text-center" onclick="showSection('appointment')">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                    <i class="fas fa-calendar-plus text-white text-xl"></i>
                </div>
                <h3 class="font-semibold text-gray-800 mb-1">预约咨询</h3>
                <p class="text-xs text-gray-600">预约专家时间</p>
                <div class="mt-2">
                    <span class="bg-purple-100 text-purple-600 text-xs px-2 py-1 rounded-full">可预约</span>
                </div>
            </div>

            <div class="service-card p-5 text-center" onclick="startAIChat()">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg">
                    <i class="fas fa-robot text-white text-xl"></i>
                </div>
                <h3 class="font-semibold text-gray-800 mb-1">AI助手</h3>
                <p class="text-xs text-gray-600">智能健康问答</p>
                <div class="mt-2">
                    <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full">秒回复</span>
                </div>
            </div>
        </div>

        <!-- 专家排班表 -->
        <div class="mb-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">今日专家排班</h3>
            <div class="bg-white/80 backdrop-blur-lg rounded-2xl p-5 border border-white/20">
                <div class="grid grid-cols-4 gap-2 mb-4">
                    <div class="text-center py-2 bg-blue-500 text-white rounded-lg text-sm font-medium">
                        今天
                    </div>
                    <div class="text-center py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium">
                        明天
                    </div>
                    <div class="text-center py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium">
                        后天
                    </div>
                    <div class="text-center py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium">
                        周末
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                            <span class="text-sm font-medium">09:00-12:00</span>
                        </div>
                        <span class="text-sm text-gray-600">3位专家在线</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                            <span class="text-sm font-medium">14:00-18:00</span>
                        </div>
                        <span class="text-sm text-gray-600">5位专家在线</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-purple-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                            <span class="text-sm font-medium">19:00-21:00</span>
                        </div>
                        <span class="text-sm text-gray-600">2位专家在线</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 在线专家 -->
        <div id="doctors-section">
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-xl font-bold text-gray-800">在线专家</h3>
                <div class="flex items-center gap-3">
                    <span class="text-sm text-green-600 flex items-center">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></span>
                        5位专家在线
                    </span>
                    <button class="text-blue-500 text-sm font-medium">
                        查看全部 <i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </div>
            </div>
            
            <div class="space-y-5 mb-6">
                <div class="doctor-card p-6 relative overflow-hidden">
                    <div class="absolute top-4 right-4">
                        <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">在线</span>
                    </div>
                    <div class="flex items-center">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=80&h=80&fit=crop&crop=face"
                                 alt="田中医生" class="w-20 h-20 rounded-2xl object-cover">
                            <div class="online-indicator"></div>
                            <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-video text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 flex-1">
                            <div class="flex items-center gap-2 mb-2">
                                <h4 class="font-bold text-gray-800 text-lg">田中健一 医生</h4>
                                <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full font-medium">主任医师</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">肿瘤科专家 · 东京大学医院</p>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="flex text-yellow-400 text-sm mr-2">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="text-sm text-gray-600">4.9分 (234评价)</span>
                                </div>
                                <span class="text-sm text-green-600 font-medium">响应时间: 2分钟</span>
                            </div>
                            <div class="flex items-center text-xs text-gray-500 mb-3">
                                <i class="fas fa-graduation-cap mr-1"></i>
                                <span>东京大学医学博士 · 20年临床经验</span>
                            </div>
                            <div class="flex gap-2">
                                <button class="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-2 rounded-lg text-sm font-medium" onclick="startVideoChat('田中医生')">
                                    <i class="fas fa-video mr-1"></i> 视频咨询
                                </button>
                                <button class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-lg text-sm font-medium" onclick="startChat('田中医生')">
                                    <i class="fas fa-comments mr-1"></i> 文字咨询
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="doctor-card p-6 relative overflow-hidden">
                    <div class="absolute top-4 right-4">
                        <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">在线</span>
                    </div>
                    <div class="flex items-center">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1594824475317-d3c2b8b7b3e0?w=80&h=80&fit=crop&crop=face"
                                 alt="佐藤医生" class="w-20 h-20 rounded-2xl object-cover">
                            <div class="online-indicator"></div>
                            <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-female text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 flex-1">
                            <div class="flex items-center gap-2 mb-2">
                                <h4 class="font-bold text-gray-800 text-lg">佐藤美香 医生</h4>
                                <span class="bg-pink-100 text-pink-600 text-xs px-2 py-1 rounded-full font-medium">妇科专家</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">妇科主任 · 庆应义塾大学医院</p>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="flex text-yellow-400 text-sm mr-2">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="text-sm text-gray-600">4.8分 (189评价)</span>
                                </div>
                                <span class="text-sm text-green-600 font-medium">响应时间: 1分钟</span>
                            </div>
                            <div class="flex items-center text-xs text-gray-500 mb-3">
                                <i class="fas fa-graduation-cap mr-1"></i>
                                <span>庆应大学医学博士 · 15年妇科经验</span>
                            </div>
                            <div class="flex gap-2">
                                <button class="flex-1 bg-gradient-to-r from-pink-500 to-pink-600 text-white py-2 rounded-lg text-sm font-medium" onclick="startVideoChat('佐藤医生')">
                                    <i class="fas fa-video mr-1"></i> 视频咨询
                                </button>
                                <button class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-lg text-sm font-medium" onclick="startChat('佐藤医生')">
                                    <i class="fas fa-comments mr-1"></i> 文字咨询
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客服支持 -->
        <div id="customer-service-section" class="hidden">
            <h3 class="text-lg font-bold text-gray-800 mb-4">客服支持</h3>
            
            <div class="space-y-4 mb-6">
                <div class="service-card p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-comments text-green-500"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">在线客服</h4>
                                <p class="text-sm text-gray-600">24小时在线服务</p>
                            </div>
                        </div>
                        <button class="bg-green-500 text-white px-4 py-2 rounded-lg text-sm font-medium" onclick="startCustomerChat()">
                            开始对话
                        </button>
                    </div>
                </div>
                
                <div class="service-card p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-phone text-blue-500"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">电话咨询</h4>
                                <p class="text-sm text-gray-600">************</p>
                            </div>
                        </div>
                        <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
                            拨打
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 常见问题 -->
        <div>
            <h3 class="text-lg font-bold text-gray-800 mb-4">常见问题</h3>
            
            <div class="space-y-3">
                <div class="faq-item p-4" onclick="toggleFaq(this)">
                    <div class="flex justify-between items-center">
                        <h4 class="font-medium text-gray-800">日本体检需要提前多久预约？</h4>
                        <i class="fas fa-chevron-down text-gray-400 transition-transform"></i>
                    </div>
                    <div class="faq-content">
                        <p class="text-sm text-gray-600 mt-3">建议提前2-4周预约，特别是PET-CT等精密检查项目。我们会根据您的时间安排和医院档期为您推荐最佳的体检时间。</p>
                    </div>
                </div>
                
                <div class="faq-item p-4" onclick="toggleFaq(this)">
                    <div class="flex justify-between items-center">
                        <h4 class="font-medium text-gray-800">体检报告多久能出来？</h4>
                        <i class="fas fa-chevron-down text-gray-400 transition-transform"></i>
                    </div>
                    <div class="faq-content">
                        <p class="text-sm text-gray-600 mt-3">一般体检项目当天可出初步结果，完整报告需要3-5个工作日。PET-CT等特殊检查可能需要7-10个工作日。</p>
                    </div>
                </div>
                
                <div class="faq-item p-4" onclick="toggleFaq(this)">
                    <div class="flex justify-between items-center">
                        <h4 class="font-medium text-gray-800">费用包含哪些服务？</h4>
                        <i class="fas fa-chevron-down text-gray-400 transition-transform"></i>
                    </div>
                    <div class="faq-content">
                        <p class="text-sm text-gray-600 mt-3">费用包含所有体检项目、专业翻译、医院接送、报告解读等服务。不包含往返机票和住宿费用。</p>
                    </div>
                </div>
                
                <div class="faq-item p-4" onclick="toggleFaq(this)">
                    <div class="flex justify-between items-center">
                        <h4 class="font-medium text-gray-800">语言沟通有障碍怎么办？</h4>
                        <i class="fas fa-chevron-down text-gray-400 transition-transform"></i>
                    </div>
                    <div class="faq-content">
                        <p class="text-sm text-gray-600 mt-3">我们提供专业医疗翻译服务，全程陪同体检，确保医患沟通无障碍。所有报告都会提供中文翻译版本。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 视频咨询模态框 -->
    <div id="video-modal" class="fixed inset-0 bg-black bg-opacity-90 hidden z-50">
        <div class="absolute inset-4 bg-white rounded-3xl overflow-hidden">
            <div class="flex items-center justify-between p-6 border-b bg-gray-50">
                <div class="flex items-center">
                    <img id="video-avatar" src="" alt="" class="w-12 h-12 rounded-full object-cover mr-4">
                    <div>
                        <h4 id="video-name" class="font-semibold text-gray-800"></h4>
                        <p class="text-sm text-green-600 flex items-center">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                            视频通话中
                        </p>
                    </div>
                </div>
                <button onclick="closeVideoChat()" class="w-10 h-10 bg-red-500 text-white rounded-full flex items-center justify-center">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="relative h-full bg-gray-900">
                <!-- 模拟视频画面 -->
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="text-center text-white">
                        <div class="w-32 h-32 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-video text-4xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">视频连接中...</h3>
                        <p class="text-gray-300">请稍候，正在为您接通专家</p>
                    </div>
                </div>

                <!-- 自己的视频窗口 -->
                <div class="absolute top-4 right-4 w-32 h-24 bg-gray-800 rounded-lg overflow-hidden">
                    <div class="w-full h-full flex items-center justify-center text-white">
                        <i class="fas fa-user text-2xl"></i>
                    </div>
                </div>

                <!-- 控制按钮 -->
                <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex items-center gap-4">
                    <button class="w-12 h-12 bg-gray-600 text-white rounded-full flex items-center justify-center">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <button class="w-12 h-12 bg-gray-600 text-white rounded-full flex items-center justify-center">
                        <i class="fas fa-video"></i>
                    </button>
                    <button class="w-12 h-12 bg-red-500 text-white rounded-full flex items-center justify-center" onclick="closeVideoChat()">
                        <i class="fas fa-phone-slash"></i>
                    </button>
                    <button class="w-12 h-12 bg-gray-600 text-white rounded-full flex items-center justify-center">
                        <i class="fas fa-comment"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI聊天模态框 -->
    <div id="ai-chat-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl h-4/5">
            <div class="flex items-center justify-between p-6 border-b bg-gradient-to-r from-blue-50 to-purple-50">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-robot text-white text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-800">AI健康助手</h4>
                        <p class="text-sm text-blue-600">智能问答 · 24小时在线</p>
                    </div>
                </div>
                <button onclick="closeAIChat()" class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-times text-gray-600"></i>
                </button>
            </div>

            <div id="ai-chat-messages" class="flex-1 p-6 overflow-y-auto" style="height: calc(100% - 160px);">
                <div class="chat-bubble doctor">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-robot text-blue-500 mr-2"></i>
                        <span class="font-medium text-blue-600">AI助手</span>
                    </div>
                    您好！我是您的AI健康助手，可以为您提供：<br>
                    • 健康知识咨询<br>
                    • 体检项目解读<br>
                    • 症状初步分析<br>
                    • 就医建议指导<br><br>
                    请问有什么可以帮助您的吗？
                </div>
            </div>

            <div class="p-6 border-t bg-gray-50">
                <div class="flex items-center space-x-3">
                    <button class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <i class="fas fa-microphone text-gray-600"></i>
                    </button>
                    <input type="text" id="ai-message-input" placeholder="输入您的健康问题..."
                           class="flex-1 px-4 py-3 border border-gray-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button onclick="sendAIMessage()" class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full flex items-center justify-center">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="flex items-center gap-2 mt-3">
                    <span class="text-xs text-gray-500">快速问题：</span>
                    <button class="bg-blue-100 text-blue-600 text-xs px-3 py-1 rounded-full" onclick="quickQuestion('体检前需要注意什么？')">
                        体检注意事项
                    </button>
                    <button class="bg-blue-100 text-blue-600 text-xs px-3 py-1 rounded-full" onclick="quickQuestion('PET-CT检查是什么？')">
                        PET-CT介绍
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 聊天界面模态框 -->
    <div id="chat-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl h-4/5">
            <div class="flex items-center justify-between p-6 border-b">
                <div class="flex items-center">
                    <img id="chat-avatar" src="" alt="" class="w-12 h-12 rounded-full object-cover mr-4">
                    <div>
                        <h4 id="chat-name" class="font-semibold text-gray-800"></h4>
                        <p class="text-sm text-green-600 flex items-center">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                            在线
                        </p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <button class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center" onclick="startVideoFromChat()">
                        <i class="fas fa-video text-blue-600"></i>
                    </button>
                    <button onclick="closeChat()" class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-times text-gray-600"></i>
                    </button>
                </div>
            </div>

            <div id="chat-messages" class="flex-1 p-6 overflow-y-auto" style="height: calc(100% - 160px);">
                <!-- 聊天消息将在这里显示 -->
            </div>

            <div class="p-6 border-t">
                <div class="flex items-center space-x-3">
                    <button class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <i class="fas fa-paperclip text-gray-600"></i>
                    </button>
                    <input type="text" id="message-input" placeholder="输入您的问题..."
                           class="flex-1 px-4 py-3 border border-gray-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button onclick="sendMessage()" class="w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(section) {
            document.getElementById('doctors-section').classList.add('hidden');
            document.getElementById('customer-service-section').classList.add('hidden');
            
            if (section === 'doctors') {
                document.getElementById('doctors-section').classList.remove('hidden');
            } else {
                document.getElementById('customer-service-section').classList.remove('hidden');
            }
        }
        
        function toggleFaq(element) {
            const isActive = element.classList.contains('active');
            
            // 关闭所有FAQ
            document.querySelectorAll('.faq-item').forEach(item => {
                item.classList.remove('active');
                item.querySelector('i').style.transform = 'rotate(0deg)';
            });
            
            // 如果当前不是激活状态，则激活
            if (!isActive) {
                element.classList.add('active');
                element.querySelector('i').style.transform = 'rotate(180deg)';
            }
        }
        
        function startVideoChat(doctorName) {
            document.getElementById('video-name').textContent = doctorName;
            document.getElementById('video-avatar').src = 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=60&h=60&fit=crop&crop=face';

            document.getElementById('video-modal').classList.remove('hidden');

            // 模拟连接过程
            setTimeout(() => {
                const videoContent = document.querySelector('#video-modal .absolute.inset-0');
                videoContent.innerHTML = `
                    <div class="w-full h-full bg-gray-800 flex items-center justify-center">
                        <div class="text-center text-white">
                            <div class="w-48 h-36 bg-gray-700 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=200&h=150&fit=crop&crop=face"
                                     alt="${doctorName}" class="w-full h-full object-cover rounded-lg">
                            </div>
                            <h3 class="text-xl font-semibold">${doctorName}</h3>
                            <p class="text-green-400">视频通话已连接</p>
                        </div>
                    </div>
                `;
            }, 3000);
        }

        function closeVideoChat() {
            document.getElementById('video-modal').classList.add('hidden');
        }

        function startVideoFromChat() {
            const doctorName = document.getElementById('chat-name').textContent;
            closeChat();
            setTimeout(() => startVideoChat(doctorName), 300);
        }

        function startAIChat() {
            document.getElementById('ai-chat-modal').classList.remove('hidden');
        }

        function closeAIChat() {
            document.getElementById('ai-chat-modal').classList.add('hidden');
        }

        function quickQuestion(question) {
            document.getElementById('ai-message-input').value = question;
            sendAIMessage();
        }

        function sendAIMessage() {
            const input = document.getElementById('ai-message-input');
            const message = input.value.trim();

            if (message) {
                const chatMessages = document.getElementById('ai-chat-messages');

                // 添加用户消息
                const userBubble = document.createElement('div');
                userBubble.className = 'chat-bubble user mb-4';
                userBubble.textContent = message;
                chatMessages.appendChild(userBubble);

                // 显示AI思考状态
                const thinkingBubble = document.createElement('div');
                thinkingBubble.className = 'chat-bubble doctor mb-4';
                thinkingBubble.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-robot text-blue-500 mr-2"></i>
                        <span class="font-medium text-blue-600">AI助手</span>
                    </div>
                    <div class="flex items-center mt-2">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                        </div>
                        <span class="ml-2 text-sm text-gray-600">正在分析您的问题...</span>
                    </div>
                `;
                chatMessages.appendChild(thinkingBubble);

                // 模拟AI回复
                setTimeout(() => {
                    chatMessages.removeChild(thinkingBubble);

                    const aiBubble = document.createElement('div');
                    aiBubble.className = 'chat-bubble doctor mb-4';

                    let response = '';
                    if (message.includes('体检') || message.includes('注意')) {
                        response = `
                            <div class="flex items-center mb-2">
                                <i class="fas fa-robot text-blue-500 mr-2"></i>
                                <span class="font-medium text-blue-600">AI助手</span>
                            </div>
                            关于体检前的注意事项，我为您整理如下：<br><br>
                            <strong>📋 体检前准备：</strong><br>
                            • 体检前8-12小时禁食<br>
                            • 避免剧烈运动<br>
                            • 保持充足睡眠<br>
                            • 停止服用影响检查的药物<br><br>
                            <strong>🩺 特殊检查注意：</strong><br>
                            • PET-CT检查需提前预约<br>
                            • 女性避开生理期<br>
                            • 携带既往病历资料<br><br>
                            还有其他问题吗？我可以为您详细解答。
                        `;
                    } else if (message.includes('PET') || message.includes('CT')) {
                        response = `
                            <div class="flex items-center mb-2">
                                <i class="fas fa-robot text-blue-500 mr-2"></i>
                                <span class="font-medium text-blue-600">AI助手</span>
                            </div>
                            PET-CT是目前最先进的医学影像检查技术：<br><br>
                            <strong>🔬 技术原理：</strong><br>
                            • 结合PET和CT两种技术<br>
                            • 可检测2mm以下微小病灶<br>
                            • 一次检查全身扫描<br><br>
                            <strong>🎯 主要优势：</strong><br>
                            • 早期癌症发现率高达95%<br>
                            • 无创伤、无痛苦<br>
                            • 检查时间约30-60分钟<br><br>
                            <strong>💰 费用参考：</strong><br>
                            • 在日本约15-25万日元<br>
                            • 我们的套餐包含专业解读<br><br>
                            需要了解更多详情吗？
                        `;
                    } else {
                        response = `
                            <div class="flex items-center mb-2">
                                <i class="fas fa-robot text-blue-500 mr-2"></i>
                                <span class="font-medium text-blue-600">AI助手</span>
                            </div>
                            感谢您的咨询！基于您的问题，我建议：<br><br>
                            • 如需专业医疗建议，建议咨询我们的在线专家<br>
                            • 可以预约视频咨询获得更详细的解答<br>
                            • 我们的专家团队24小时为您服务<br><br>
                            您还有其他健康相关的问题吗？
                        `;
                    }

                    aiBubble.innerHTML = response;
                    chatMessages.appendChild(aiBubble);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }, 2000);

                input.value = '';
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }

        function startChat(doctorName) {
            document.getElementById('chat-name').textContent = doctorName;
            document.getElementById('chat-avatar').src = 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=60&h=60&fit=crop&crop=face';

            // 清空聊天记录并添加欢迎消息
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.innerHTML = `
                <div class="chat-bubble doctor mb-4">
                    <div class="flex items-center mb-2">
                        <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=30&h=30&fit=crop&crop=face"
                             alt="${doctorName}" class="w-8 h-8 rounded-full mr-2">
                        <span class="font-medium">${doctorName}</span>
                    </div>
                    您好！我是${doctorName}，很高兴为您服务。请问有什么健康问题需要咨询吗？<br><br>
                    我可以为您提供：<br>
                    • 专业医疗建议<br>
                    • 体检项目推荐<br>
                    • 健康状况评估<br>
                    • 治疗方案建议
                </div>
            `;

            document.getElementById('chat-modal').classList.remove('hidden');
        }
        
        function startCustomerChat() {
            document.getElementById('chat-name').textContent = '客服小助手';
            document.getElementById('chat-avatar').src = 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face';
            
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.innerHTML = `
                <div class="chat-bubble doctor">
                    您好！欢迎咨询日本医疗健康体检服务，我是您的专属客服。请问有什么可以帮助您的吗？
                </div>
            `;
            
            document.getElementById('chat-modal').classList.remove('hidden');
        }
        
        function closeChat() {
            document.getElementById('chat-modal').classList.add('hidden');
        }
        
        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (message) {
                const chatMessages = document.getElementById('chat-messages');
                
                // 添加用户消息
                const userBubble = document.createElement('div');
                userBubble.className = 'chat-bubble user';
                userBubble.textContent = message;
                chatMessages.appendChild(userBubble);
                
                // 模拟回复
                setTimeout(() => {
                    const doctorBubble = document.createElement('div');
                    doctorBubble.className = 'chat-bubble doctor';
                    doctorBubble.textContent = '感谢您的咨询，我正在为您分析问题，请稍等片刻...';
                    chatMessages.appendChild(doctorBubble);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }, 1000);
                
                input.value = '';
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }
        
        // 回车发送消息
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        document.getElementById('ai-message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendAIMessage();
            }
        });

        // 页面加载动画
        window.addEventListener('load', function() {
            // 服务卡片动画
            document.querySelectorAll('.service-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease-out';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 专家卡片动画
            document.querySelectorAll('.doctor-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateX(-20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease-out';
                    card.style.opacity = '1';
                    card.style.transform = 'translateX(0)';
                }, 500 + index * 200);
            });
        });
    </script>
</body>
</html>
