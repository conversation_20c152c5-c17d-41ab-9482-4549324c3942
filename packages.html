<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>体检套餐</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .category-tab {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .category-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .category-tab.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .package-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .package-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .package-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .package-card:hover::before {
            opacity: 1;
        }

        .package-card.selected {
            border-color: #667eea;
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.2);
        }

        .package-card.selected::before {
            opacity: 1;
        }
        
        .premium-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #8b5a00;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .price-highlight {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(243, 244, 246, 0.5);
            transition: all 0.2s ease;
        }

        .feature-item:hover {
            background: rgba(102, 126, 234, 0.05);
            padding-left: 8px;
            border-radius: 8px;
        }

        .feature-item:last-child {
            border-bottom: none;
        }

        .filter-dropdown {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(20px);
        }

        .compare-panel {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-radius: 24px 24px 0 0;
            box-shadow: 0 -8px 32px rgba(0,0,0,0.1);
            transform: translateY(100%);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
        }

        .compare-panel.active {
            transform: translateY(0);
        }

        .floating-compare-btn {
            position: fixed;
            bottom: 120px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            width: 56px;
            height: 56px;
            display: flex;
            items-center: center;
            justify-content: center;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
            z-index: 999;
            transition: all 0.3s ease;
        }

        .floating-compare-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.6);
        }

        .price-calculator {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            padding: 20px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50">
    <!-- 顶部标题栏 -->
    <div class="bg-white/80 backdrop-blur-lg px-6 py-5 border-b border-white/20">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">体检套餐</h1>
                <p class="text-sm text-gray-600 mt-1">专业精密 · 值得信赖</p>
            </div>
            <div class="flex items-center gap-3">
                <button class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center" onclick="toggleFilter()">
                    <i class="fas fa-filter text-gray-600"></i>
                </button>
                <button class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center" onclick="toggleSort()">
                    <i class="fas fa-sort text-gray-600"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 高级筛选面板 -->
    <div id="filter-panel" class="hidden bg-white/90 backdrop-blur-lg px-6 py-4 border-b border-white/20">
        <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">价格范围</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                    <option>全部价格</option>
                    <option>10,000-20,000</option>
                    <option>20,000-30,000</option>
                    <option>30,000以上</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">检查时长</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm">
                    <option>全部时长</option>
                    <option>半天</option>
                    <option>1天</option>
                    <option>2天以上</option>
                </select>
            </div>
        </div>
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
                <label class="flex items-center">
                    <input type="checkbox" class="mr-2">
                    <span class="text-sm">包含PET-CT</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-2">
                    <span class="text-sm">包含MRI</span>
                </label>
            </div>
            <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
                应用筛选
            </button>
        </div>
    </div>

    <!-- 分类标签 -->
    <div class="bg-white/80 backdrop-blur-lg px-6 py-4 border-b border-white/20">
        <div class="flex space-x-3 overflow-x-auto">
            <button class="category-tab active px-5 py-3 rounded-2xl text-sm font-semibold whitespace-nowrap"
                    onclick="switchCategory('all')">全部套餐</button>
            <button class="category-tab px-5 py-3 rounded-2xl text-sm font-semibold whitespace-nowrap bg-gray-100 text-gray-600"
                    onclick="switchCategory('premium')">精密体检</button>
            <button class="category-tab px-5 py-3 rounded-2xl text-sm font-semibold whitespace-nowrap bg-gray-100 text-gray-600"
                    onclick="switchCategory('women')">女性专项</button>
            <button class="category-tab px-5 py-3 rounded-2xl text-sm font-semibold whitespace-nowrap bg-gray-100 text-gray-600"
                    onclick="switchCategory('men')">男性专项</button>
            <button class="category-tab px-5 py-3 rounded-2xl text-sm font-semibold whitespace-nowrap bg-gray-100 text-gray-600"
                    onclick="switchCategory('senior')">中老年</button>
        </div>
    </div>

    <!-- 套餐列表 -->
    <div class="px-6 py-6 space-y-6">
        <!-- PET-CT精密体检套餐 -->
        <div class="package-card p-6" data-category="premium" data-price="28800" data-duration="2">
            <div class="absolute top-4 right-4 flex items-center gap-2">
                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">热销</span>
                <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center compare-btn" onclick="toggleCompare(this, 'pet-ct')">
                    <i class="fas fa-balance-scale text-gray-600 text-xs"></i>
                </button>
            </div>

            <div class="flex justify-between items-start mb-4">
                <div class="flex items-center gap-3">
                    <h3 class="text-xl font-bold text-gray-800">PET-CT全身精密体检</h3>
                    <span class="premium-badge">Premium</span>
                </div>
                <div class="text-right">
                    <div class="price-highlight text-3xl">¥28,800</div>
                    <div class="text-sm text-gray-500 line-through">¥32,000</div>
                    <div class="text-xs text-green-600 font-medium">节省 ¥3,200</div>
                </div>
            </div>

            <div class="flex items-center gap-5 mb-5">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=120&h=100&fit=crop"
                         alt="PET-CT检查" class="w-28 h-24 rounded-2xl object-cover">
                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-crown text-white text-xs"></i>
                    </div>
                </div>
                <div class="flex-1">
                    <p class="text-sm text-gray-600 mb-3 leading-relaxed">最先进的癌症早期筛查技术，可检测2mm以下微小肿瘤，全面保障您的健康</p>
                    <div class="flex items-center justify-between text-xs">
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-map-marker-alt mr-1 text-blue-500"></i>
                            <span>东京大学医院 · 2天1夜</span>
                        </div>
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 mr-1">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-gray-600">4.9 (128评价)</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-2 gap-3 mb-5">
                <div class="feature-item bg-green-50 rounded-lg p-3">
                    <i class="fas fa-microscope text-green-500 mr-3 text-lg"></i>
                    <div>
                        <div class="text-sm font-medium">PET-CT全身扫描</div>
                        <div class="text-xs text-gray-600">2mm精度检测</div>
                    </div>
                </div>
                <div class="feature-item bg-blue-50 rounded-lg p-3">
                    <i class="fas fa-brain text-blue-500 mr-3 text-lg"></i>
                    <div>
                        <div class="text-sm font-medium">MRI脑部检查</div>
                        <div class="text-xs text-gray-600">高清成像</div>
                    </div>
                </div>
                <div class="feature-item bg-red-50 rounded-lg p-3">
                    <i class="fas fa-heartbeat text-red-500 mr-3 text-lg"></i>
                    <div>
                        <div class="text-sm font-medium">心脏超声检查</div>
                        <div class="text-xs text-gray-600">功能评估</div>
                    </div>
                </div>
                <div class="feature-item bg-purple-50 rounded-lg p-3">
                    <i class="fas fa-vial text-purple-500 mr-3 text-lg"></i>
                    <div>
                        <div class="text-sm font-medium">300+项血检</div>
                        <div class="text-xs text-gray-600">全面筛查</div>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center text-sm text-green-600">
                    <i class="fas fa-users mr-1"></i>
                    <span>本月已有 89 人预约</span>
                </div>
                <button class="text-blue-500 text-sm font-medium" onclick="showPackageDetail('pet-ct')">
                    查看详情 <i class="fas fa-chevron-right ml-1"></i>
                </button>
            </div>

            <div class="flex gap-3">
                <button class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all">
                    立即预约
                </button>
                <button class="px-6 bg-gray-100 text-gray-700 py-3 rounded-xl font-medium hover:bg-gray-200 transition-all">
                    咨询
                </button>
            </div>
        </div>

        <!-- 高端女性体检套餐 -->
        <div class="package-card p-5" data-category="women">
            <div class="flex justify-between items-start mb-3">
                <h3 class="text-lg font-bold text-gray-800">高端女性专项体检</h3>
                <div class="text-right">
                    <div class="price-highlight text-2xl">¥18,800</div>
                    <div class="text-xs text-gray-500 line-through">¥21,000</div>
                </div>
            </div>
            
            <div class="flex items-center gap-4 mb-4">
                <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=100&h=80&fit=crop" 
                     alt="女性体检" class="w-24 h-20 rounded-lg object-cover">
                <div class="flex-1">
                    <p class="text-sm text-gray-600 mb-2">专为女性设计的全面健康检查，关注女性特有疾病</p>
                    <div class="flex items-center text-xs text-gray-500">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        <span>大阪 · 1天</span>
                        <span class="ml-4">⭐ 4.8 (89评价)</span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-1 mb-4">
                <div class="feature-item">
                    <i class="fas fa-check-circle text-pink-500 mr-3"></i>
                    <span class="text-sm">乳腺癌筛查(钼靶+超声)</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-pink-500 mr-3"></i>
                    <span class="text-sm">宫颈癌筛查(TCT+HPV)</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-pink-500 mr-3"></i>
                    <span class="text-sm">骨密度检测</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-pink-500 mr-3"></i>
                    <span class="text-sm">甲状腺功能检查</span>
                </div>
            </div>
            
            <button class="w-full bg-gradient-to-r from-pink-500 to-rose-600 text-white py-3 rounded-lg font-medium">
                立即预约
            </button>
        </div>

        <!-- 男性专项体检套餐 -->
        <div class="package-card p-5" data-category="men">
            <div class="flex justify-between items-start mb-3">
                <h3 class="text-lg font-bold text-gray-800">男性专项体检套餐</h3>
                <div class="text-right">
                    <div class="price-highlight text-2xl">¥16,800</div>
                    <div class="text-xs text-gray-500 line-through">¥19,000</div>
                </div>
            </div>
            
            <div class="flex items-center gap-4 mb-4">
                <img src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=100&h=80&fit=crop" 
                     alt="男性体检" class="w-24 h-20 rounded-lg object-cover">
                <div class="flex-1">
                    <p class="text-sm text-gray-600 mb-2">针对男性高发疾病的专业检查，关注心血管健康</p>
                    <div class="flex items-center text-xs text-gray-500">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        <span>名古屋 · 1天</span>
                        <span class="ml-4">⭐ 4.7 (76评价)</span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-1 mb-4">
                <div class="feature-item">
                    <i class="fas fa-check-circle text-blue-500 mr-3"></i>
                    <span class="text-sm">前列腺癌筛查(PSA+超声)</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-blue-500 mr-3"></i>
                    <span class="text-sm">心脏冠脉CT检查</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-blue-500 mr-3"></i>
                    <span class="text-sm">肺部低剂量CT</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-blue-500 mr-3"></i>
                    <span class="text-sm">肝功能全套检查</span>
                </div>
            </div>
            
            <button class="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 rounded-lg font-medium">
                立即预约
            </button>
        </div>

        <!-- 中老年健康体检套餐 -->
        <div class="package-card p-5" data-category="senior">
            <div class="flex justify-between items-start mb-3">
                <h3 class="text-lg font-bold text-gray-800">中老年健康体检</h3>
                <div class="text-right">
                    <div class="price-highlight text-2xl">¥12,800</div>
                    <div class="text-xs text-gray-500 line-through">¥15,000</div>
                </div>
            </div>
            
            <div class="flex items-center gap-4 mb-4">
                <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=100&h=80&fit=crop" 
                     alt="中老年体检" class="w-24 h-20 rounded-lg object-cover">
                <div class="flex-1">
                    <p class="text-sm text-gray-600 mb-2">专为50岁以上人群设计，重点关注慢性病筛查</p>
                    <div class="flex items-center text-xs text-gray-500">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        <span>京都 · 1天</span>
                        <span class="ml-4">⭐ 4.6 (54评价)</span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-1 mb-4">
                <div class="feature-item">
                    <i class="fas fa-check-circle text-orange-500 mr-3"></i>
                    <span class="text-sm">心脑血管全面检查</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-orange-500 mr-3"></i>
                    <span class="text-sm">糖尿病并发症筛查</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-orange-500 mr-3"></i>
                    <span class="text-sm">骨质疏松检测</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-orange-500 mr-3"></i>
                    <span class="text-sm">认知功能评估</span>
                </div>
            </div>
            
            <button class="w-full bg-gradient-to-r from-orange-500 to-red-600 text-white py-3 rounded-lg font-medium">
                立即预约
            </button>
        </div>
    </div>

    <!-- 浮动对比按钮 -->
    <div id="floating-compare" class="floating-compare-btn hidden" onclick="showComparePanel()">
        <i class="fas fa-balance-scale text-xl"></i>
        <div class="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs" id="compare-count">0</div>
    </div>

    <!-- 对比面板 -->
    <div id="compare-panel" class="compare-panel">
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-800">套餐对比</h3>
                <button onclick="hideComparePanel()" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-times text-gray-600"></i>
                </button>
            </div>
            <div id="compare-content" class="grid grid-cols-2 gap-4">
                <!-- 对比内容将在这里动态生成 -->
            </div>
            <div class="mt-4 flex gap-3">
                <button class="flex-1 bg-blue-500 text-white py-3 rounded-lg font-medium">
                    预约选中套餐
                </button>
                <button onclick="clearCompare()" class="px-6 bg-gray-200 text-gray-700 py-3 rounded-lg font-medium">
                    清空对比
                </button>
            </div>
        </div>
    </div>

    <!-- 价格计算器模态框 -->
    <div id="price-calculator-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl h-3/4">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-800">价格计算器</h3>
                    <button onclick="hidePriceCalculator()" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-times text-gray-600"></i>
                    </button>
                </div>

                <div class="price-calculator mb-6">
                    <h4 class="font-semibold mb-4">自定义您的体检套餐</h4>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">基础套餐</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                <option value="15000">基础体检 - ¥15,000</option>
                                <option value="25000">精密体检 - ¥25,000</option>
                                <option value="35000">顶级体检 - ¥35,000</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">附加项目</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="8000">
                                    <span class="text-sm">PET-CT检查 (+¥8,000)</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="3000">
                                    <span class="text-sm">心脏MRI (+¥3,000)</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="2000">
                                    <span class="text-sm">基因检测 (+¥2,000)</span>
                                </label>
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex justify-between items-center">
                                <span class="font-medium">预估总价：</span>
                                <span class="text-2xl font-bold text-blue-600" id="total-price">¥15,000</span>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="w-full bg-blue-500 text-white py-3 rounded-lg font-medium">
                    生成定制套餐
                </button>
            </div>
        </div>
    </div>

    <script>
        let compareList = [];

        function switchCategory(category) {
            // 更新标签状态
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
                tab.classList.add('bg-gray-100', 'text-gray-600');
            });

            event.target.classList.add('active');
            event.target.classList.remove('bg-gray-100', 'text-gray-600');

            // 显示/隐藏套餐
            const packages = document.querySelectorAll('.package-card');
            packages.forEach(pkg => {
                if (category === 'all' || pkg.dataset.category === category) {
                    pkg.style.display = 'block';
                    pkg.style.animation = 'fadeInUp 0.5s ease-out';
                } else {
                    pkg.style.display = 'none';
                }
            });
        }

        function toggleFilter() {
            const panel = document.getElementById('filter-panel');
            panel.classList.toggle('hidden');
        }

        function toggleSort() {
            // 实现排序功能
            const packages = Array.from(document.querySelectorAll('.package-card'));
            const container = packages[0].parentNode;

            packages.sort((a, b) => {
                const priceA = parseInt(a.dataset.price);
                const priceB = parseInt(b.dataset.price);
                return priceA - priceB;
            });

            packages.forEach(pkg => container.appendChild(pkg));
        }

        function toggleCompare(button, packageId) {
            const isSelected = button.classList.contains('selected');

            if (isSelected) {
                button.classList.remove('selected');
                button.style.background = '#f3f4f6';
                compareList = compareList.filter(id => id !== packageId);
            } else {
                if (compareList.length < 3) {
                    button.classList.add('selected');
                    button.style.background = '#3b82f6';
                    button.querySelector('i').style.color = 'white';
                    compareList.push(packageId);
                } else {
                    alert('最多只能对比3个套餐');
                    return;
                }
            }

            updateCompareButton();
        }

        function updateCompareButton() {
            const floatingBtn = document.getElementById('floating-compare');
            const countBadge = document.getElementById('compare-count');

            if (compareList.length > 0) {
                floatingBtn.classList.remove('hidden');
                countBadge.textContent = compareList.length;
            } else {
                floatingBtn.classList.add('hidden');
            }
        }

        function showComparePanel() {
            document.getElementById('compare-panel').classList.add('active');
        }

        function hideComparePanel() {
            document.getElementById('compare-panel').classList.remove('active');
        }

        function clearCompare() {
            compareList = [];
            document.querySelectorAll('.compare-btn').forEach(btn => {
                btn.classList.remove('selected');
                btn.style.background = '#f3f4f6';
                btn.querySelector('i').style.color = '#6b7280';
            });
            updateCompareButton();
            hideComparePanel();
        }

        function showPackageDetail(packageId) {
            // 显示套餐详情
            alert('显示 ' + packageId + ' 套餐详情');
        }

        function showPriceCalculator() {
            document.getElementById('price-calculator-modal').classList.remove('hidden');
        }

        function hidePriceCalculator() {
            document.getElementById('price-calculator-modal').classList.add('hidden');
        }

        // 价格计算器逻辑
        document.addEventListener('DOMContentLoaded', function() {
            const baseSelect = document.querySelector('#price-calculator-modal select');
            const checkboxes = document.querySelectorAll('#price-calculator-modal input[type="checkbox"]');
            const totalPrice = document.getElementById('total-price');

            function updatePrice() {
                let total = parseInt(baseSelect.value);
                checkboxes.forEach(cb => {
                    if (cb.checked) {
                        total += parseInt(cb.value);
                    }
                });
                totalPrice.textContent = '¥' + total.toLocaleString();
            }

            baseSelect.addEventListener('change', updatePrice);
            checkboxes.forEach(cb => cb.addEventListener('change', updatePrice));
        });

        // 添加页面加载动画
        window.addEventListener('load', function() {
            document.querySelectorAll('.package-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease-out';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
