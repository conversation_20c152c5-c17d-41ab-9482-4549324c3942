<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>体检套餐</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .category-tab {
            transition: all 0.3s ease;
        }
        
        .category-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .package-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .package-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        
        .premium-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #8b5a00;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
        }
        
        .price-highlight {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部标题栏 -->
    <div class="bg-white px-4 py-4 border-b border-gray-100">
        <h1 class="text-xl font-bold text-gray-800 text-center">体检套餐</h1>
    </div>

    <!-- 分类标签 -->
    <div class="bg-white px-4 py-3 border-b border-gray-100">
        <div class="flex space-x-2 overflow-x-auto">
            <button class="category-tab active px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap" 
                    onclick="switchCategory('all')">全部套餐</button>
            <button class="category-tab px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap bg-gray-100 text-gray-600" 
                    onclick="switchCategory('premium')">精密体检</button>
            <button class="category-tab px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap bg-gray-100 text-gray-600" 
                    onclick="switchCategory('women')">女性专项</button>
            <button class="category-tab px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap bg-gray-100 text-gray-600" 
                    onclick="switchCategory('men')">男性专项</button>
            <button class="category-tab px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap bg-gray-100 text-gray-600" 
                    onclick="switchCategory('senior')">中老年</button>
        </div>
    </div>

    <!-- 套餐列表 -->
    <div class="px-4 py-4 space-y-4">
        <!-- PET-CT精密体检套餐 -->
        <div class="package-card p-5" data-category="premium">
            <div class="flex justify-between items-start mb-3">
                <div class="flex items-center gap-2">
                    <h3 class="text-lg font-bold text-gray-800">PET-CT全身精密体检</h3>
                    <span class="premium-badge">Premium</span>
                </div>
                <div class="text-right">
                    <div class="price-highlight text-2xl">¥28,800</div>
                    <div class="text-xs text-gray-500 line-through">¥32,000</div>
                </div>
            </div>
            
            <div class="flex items-center gap-4 mb-4">
                <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=100&h=80&fit=crop" 
                     alt="PET-CT检查" class="w-24 h-20 rounded-lg object-cover">
                <div class="flex-1">
                    <p class="text-sm text-gray-600 mb-2">最先进的癌症早期筛查技术，可检测2mm以下微小肿瘤</p>
                    <div class="flex items-center text-xs text-gray-500">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        <span>东京都 · 2天1夜</span>
                        <span class="ml-4">⭐ 4.9 (128评价)</span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-1 mb-4">
                <div class="feature-item">
                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                    <span class="text-sm">PET-CT全身扫描</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                    <span class="text-sm">MRI脑部/腹部检查</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                    <span class="text-sm">心脏超声检查</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                    <span class="text-sm">300+项血液检测</span>
                </div>
            </div>
            
            <button class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-lg font-medium">
                立即预约
            </button>
        </div>

        <!-- 高端女性体检套餐 -->
        <div class="package-card p-5" data-category="women">
            <div class="flex justify-between items-start mb-3">
                <h3 class="text-lg font-bold text-gray-800">高端女性专项体检</h3>
                <div class="text-right">
                    <div class="price-highlight text-2xl">¥18,800</div>
                    <div class="text-xs text-gray-500 line-through">¥21,000</div>
                </div>
            </div>
            
            <div class="flex items-center gap-4 mb-4">
                <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=100&h=80&fit=crop" 
                     alt="女性体检" class="w-24 h-20 rounded-lg object-cover">
                <div class="flex-1">
                    <p class="text-sm text-gray-600 mb-2">专为女性设计的全面健康检查，关注女性特有疾病</p>
                    <div class="flex items-center text-xs text-gray-500">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        <span>大阪 · 1天</span>
                        <span class="ml-4">⭐ 4.8 (89评价)</span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-1 mb-4">
                <div class="feature-item">
                    <i class="fas fa-check-circle text-pink-500 mr-3"></i>
                    <span class="text-sm">乳腺癌筛查(钼靶+超声)</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-pink-500 mr-3"></i>
                    <span class="text-sm">宫颈癌筛查(TCT+HPV)</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-pink-500 mr-3"></i>
                    <span class="text-sm">骨密度检测</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-pink-500 mr-3"></i>
                    <span class="text-sm">甲状腺功能检查</span>
                </div>
            </div>
            
            <button class="w-full bg-gradient-to-r from-pink-500 to-rose-600 text-white py-3 rounded-lg font-medium">
                立即预约
            </button>
        </div>

        <!-- 男性专项体检套餐 -->
        <div class="package-card p-5" data-category="men">
            <div class="flex justify-between items-start mb-3">
                <h3 class="text-lg font-bold text-gray-800">男性专项体检套餐</h3>
                <div class="text-right">
                    <div class="price-highlight text-2xl">¥16,800</div>
                    <div class="text-xs text-gray-500 line-through">¥19,000</div>
                </div>
            </div>
            
            <div class="flex items-center gap-4 mb-4">
                <img src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=100&h=80&fit=crop" 
                     alt="男性体检" class="w-24 h-20 rounded-lg object-cover">
                <div class="flex-1">
                    <p class="text-sm text-gray-600 mb-2">针对男性高发疾病的专业检查，关注心血管健康</p>
                    <div class="flex items-center text-xs text-gray-500">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        <span>名古屋 · 1天</span>
                        <span class="ml-4">⭐ 4.7 (76评价)</span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-1 mb-4">
                <div class="feature-item">
                    <i class="fas fa-check-circle text-blue-500 mr-3"></i>
                    <span class="text-sm">前列腺癌筛查(PSA+超声)</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-blue-500 mr-3"></i>
                    <span class="text-sm">心脏冠脉CT检查</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-blue-500 mr-3"></i>
                    <span class="text-sm">肺部低剂量CT</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-blue-500 mr-3"></i>
                    <span class="text-sm">肝功能全套检查</span>
                </div>
            </div>
            
            <button class="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 rounded-lg font-medium">
                立即预约
            </button>
        </div>

        <!-- 中老年健康体检套餐 -->
        <div class="package-card p-5" data-category="senior">
            <div class="flex justify-between items-start mb-3">
                <h3 class="text-lg font-bold text-gray-800">中老年健康体检</h3>
                <div class="text-right">
                    <div class="price-highlight text-2xl">¥12,800</div>
                    <div class="text-xs text-gray-500 line-through">¥15,000</div>
                </div>
            </div>
            
            <div class="flex items-center gap-4 mb-4">
                <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=100&h=80&fit=crop" 
                     alt="中老年体检" class="w-24 h-20 rounded-lg object-cover">
                <div class="flex-1">
                    <p class="text-sm text-gray-600 mb-2">专为50岁以上人群设计，重点关注慢性病筛查</p>
                    <div class="flex items-center text-xs text-gray-500">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        <span>京都 · 1天</span>
                        <span class="ml-4">⭐ 4.6 (54评价)</span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-1 mb-4">
                <div class="feature-item">
                    <i class="fas fa-check-circle text-orange-500 mr-3"></i>
                    <span class="text-sm">心脑血管全面检查</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-orange-500 mr-3"></i>
                    <span class="text-sm">糖尿病并发症筛查</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-orange-500 mr-3"></i>
                    <span class="text-sm">骨质疏松检测</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle text-orange-500 mr-3"></i>
                    <span class="text-sm">认知功能评估</span>
                </div>
            </div>
            
            <button class="w-full bg-gradient-to-r from-orange-500 to-red-600 text-white py-3 rounded-lg font-medium">
                立即预约
            </button>
        </div>
    </div>

    <script>
        function switchCategory(category) {
            // 更新标签状态
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
                tab.classList.add('bg-gray-100', 'text-gray-600');
            });
            
            event.target.classList.add('active');
            event.target.classList.remove('bg-gray-100', 'text-gray-600');
            
            // 显示/隐藏套餐
            const packages = document.querySelectorAll('.package-card');
            packages.forEach(pkg => {
                if (category === 'all' || pkg.dataset.category === category) {
                    pkg.style.display = 'block';
                } else {
                    pkg.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
