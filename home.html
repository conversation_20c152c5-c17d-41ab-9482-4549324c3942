<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .banner-slider {
            position: relative;
            overflow: hidden;
            border-radius: 24px;
            margin: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .slide {
            display: none;
            animation: slideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
            z-index: 1;
        }

        .slide-content {
            position: relative;
            z-index: 2;
        }

        .service-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 24px;
            color: white;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .service-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 16px 48px rgba(102, 126, 234, 0.4);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .service-card:hover::before {
            transform: scale(1.2);
        }

        .package-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255,255,255,0.8);
            backdrop-filter: blur(20px);
        }

        .package-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .price-tag {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 6px 16px;
            border-radius: 25px;
            font-size: 13px;
            font-weight: 700;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .stats-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.1);
        }

        .floating-element {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .certification-badge {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50">
    <!-- 顶部搜索栏 -->
    <div class="bg-white/80 backdrop-blur-lg px-6 py-4 border-b border-white/20">
        <div class="flex items-center justify-between mb-3">
            <div>
                <h1 class="text-xl font-bold gradient-text">日本精密体检</h1>
                <p class="text-sm text-gray-600">专业 · 精准 · 安心</p>
            </div>
            <div class="flex items-center gap-3">
                <div class="certification-badge">
                    <i class="fas fa-shield-check"></i>
                    <span>JCI认证</span>
                </div>
                <button class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-bell text-gray-600"></i>
                </button>
            </div>
        </div>
        <div class="flex items-center bg-gray-100/80 backdrop-blur-sm rounded-2xl px-5 py-3">
            <i class="fas fa-search text-gray-400 mr-3"></i>
            <input type="text" placeholder="搜索体检项目、医院、专家..." class="bg-transparent flex-1 outline-none text-sm font-medium">
            <button class="ml-2 text-blue-500">
                <i class="fas fa-microphone"></i>
            </button>
        </div>
    </div>

    <!-- 数据统计横幅 -->
    <div class="px-6 py-4">
        <div class="grid grid-cols-3 gap-4">
            <div class="stats-card floating-element">
                <div class="text-2xl font-bold text-blue-600 mb-1">50,000+</div>
                <div class="text-xs text-gray-600">服务客户</div>
            </div>
            <div class="stats-card floating-element" style="animation-delay: 0.5s;">
                <div class="text-2xl font-bold text-green-600 mb-1">99.8%</div>
                <div class="text-xs text-gray-600">满意度</div>
            </div>
            <div class="stats-card floating-element" style="animation-delay: 1s;">
                <div class="text-2xl font-bold text-purple-600 mb-1">2mm</div>
                <div class="text-xs text-gray-600">检测精度</div>
            </div>
        </div>
    </div>

    <!-- 高端轮播图 -->
    <div class="banner-slider h-56 relative">
        <div class="slide active">
            <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=500&h=300&fit=crop"
                 alt="日本精密体检" class="w-full h-full object-cover">
            <div class="slide-content absolute inset-0 flex items-center justify-center">
                <div class="text-center text-white px-6">
                    <div class="mb-4">
                        <span class="inline-block bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium mb-3">
                            🏥 东京大学医院
                        </span>
                    </div>
                    <h2 class="text-2xl font-bold mb-3">PET-CT精密体检</h2>
                    <p class="text-sm opacity-90 mb-4">2mm肿瘤早期发现 · 世界领先技术</p>
                    <button class="bg-white/20 backdrop-blur-sm border border-white/30 text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-white/30 transition-all">
                        了解详情 →
                    </button>
                </div>
            </div>
        </div>
        <div class="slide">
            <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=500&h=300&fit=crop"
                 alt="高端医疗服务" class="w-full h-full object-cover">
            <div class="slide-content absolute inset-0 flex items-center justify-center">
                <div class="text-center text-white px-6">
                    <div class="mb-4">
                        <span class="inline-block bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium mb-3">
                            ⭐ 五星级服务
                        </span>
                    </div>
                    <h2 class="text-2xl font-bold mb-3">专家一对一服务</h2>
                    <p class="text-sm opacity-90 mb-4">顶级医师团队 · 全程贴心陪护</p>
                    <button class="bg-white/20 backdrop-blur-sm border border-white/30 text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-white/30 transition-all">
                        预约专家 →
                    </button>
                </div>
            </div>
        </div>
        <div class="slide">
            <img src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=500&h=300&fit=crop"
                 alt="医疗旅游" class="w-full h-full object-cover">
            <div class="slide-content absolute inset-0 flex items-center justify-center">
                <div class="text-center text-white px-6">
                    <div class="mb-4">
                        <span class="inline-block bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium mb-3">
                            🌸 医疗旅游
                        </span>
                    </div>
                    <h2 class="text-2xl font-bold mb-3">健康日本行</h2>
                    <p class="text-sm opacity-90 mb-4">体检 + 旅游 · 身心双重享受</p>
                    <button class="bg-white/20 backdrop-blur-sm border border-white/30 text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-white/30 transition-all">
                        定制行程 →
                    </button>
                </div>
            </div>
        </div>

        <!-- 高端轮播指示器 -->
        <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3">
            <div class="w-8 h-2 bg-white rounded-full opacity-100 transition-all"></div>
            <div class="w-2 h-2 bg-white rounded-full opacity-50 transition-all"></div>
            <div class="w-2 h-2 bg-white rounded-full opacity-50 transition-all"></div>
        </div>

        <!-- 轮播控制按钮 -->
        <button class="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <!-- 医院认证展示 -->
    <div class="px-6 py-4">
        <div class="bg-white/80 backdrop-blur-lg rounded-2xl p-5 border border-white/20">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold gradient-text">合作医院认证</h3>
                <span class="text-sm text-gray-600">权威保障</span>
            </div>
            <div class="flex items-center justify-between">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-certificate text-blue-600"></i>
                    </div>
                    <span class="text-xs text-gray-600">JCI认证</span>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-award text-green-600"></i>
                    </div>
                    <span class="text-xs text-gray-600">ISO认证</span>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-medal text-purple-600"></i>
                    </div>
                    <span class="text-xs text-gray-600">厚生省认可</span>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-shield-check text-orange-600"></i>
                    </div>
                    <span class="text-xs text-gray-600">国际标准</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 服务亮点 -->
    <div class="px-6 py-4">
        <div class="flex items-center justify-between mb-5">
            <h3 class="text-xl font-bold text-gray-800">为什么选择我们</h3>
            <div class="flex items-center text-sm text-gray-600">
                <i class="fas fa-users mr-1"></i>
                <span>50,000+ 信赖之选</span>
            </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
            <div class="service-card">
                <div class="flex items-center mb-3">
                    <i class="fas fa-microscope text-3xl mr-3"></i>
                    <div class="w-2 h-2 bg-white/30 rounded-full animate-pulse"></div>
                </div>
                <h4 class="font-bold mb-2 text-lg">精密检测</h4>
                <p class="text-sm opacity-90 leading-relaxed">PET-CT可检测2mm微小肿瘤</p>
                <div class="mt-3 text-xs opacity-75">检测精度: 99.8%</div>
            </div>
            <div class="service-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="flex items-center mb-3">
                    <i class="fas fa-user-md text-3xl mr-3"></i>
                    <div class="w-2 h-2 bg-white/30 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                </div>
                <h4 class="font-bold mb-2 text-lg">专家团队</h4>
                <p class="text-sm opacity-90 leading-relaxed">顶级医师一对一专业服务</p>
                <div class="mt-3 text-xs opacity-75">平均经验: 15年+</div>
            </div>
            <div class="service-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="flex items-center mb-3">
                    <i class="fas fa-shield-alt text-3xl mr-3"></i>
                    <div class="w-2 h-2 bg-white/30 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                </div>
                <h4 class="font-bold mb-2 text-lg">早期筛查</h4>
                <p class="text-sm opacity-90 leading-relaxed">300+种癌症全面筛查</p>
                <div class="mt-3 text-xs opacity-75">筛查覆盖率: 95%+</div>
            </div>
            <div class="service-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="flex items-center mb-3">
                    <i class="fas fa-concierge-bell text-3xl mr-3"></i>
                    <div class="w-2 h-2 bg-white/30 rounded-full animate-pulse" style="animation-delay: 1.5s;"></div>
                </div>
                <h4 class="font-bold mb-2 text-lg">贵宾服务</h4>
                <p class="text-sm opacity-90 leading-relaxed">全程贴心陪护服务</p>
                <div class="mt-3 text-xs opacity-75">满意度: 99.8%</div>
            </div>
        </div>
    </div>

    <!-- 热门套餐 -->
    <div class="px-6 pb-6">
        <div class="flex justify-between items-center mb-5">
            <div>
                <h3 class="text-xl font-bold text-gray-800">热门体检套餐</h3>
                <p class="text-sm text-gray-600 mt-1">精选高品质体检项目</p>
            </div>
            <button class="text-blue-500 text-sm font-medium flex items-center">
                查看全部 <i class="fas fa-chevron-right ml-1"></i>
            </button>
        </div>

        <div class="space-y-5">
            <div class="package-card p-6 relative overflow-hidden">
                <div class="absolute top-4 right-4">
                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">热销</span>
                </div>
                <div class="flex">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=100&h=100&fit=crop"
                             alt="PET-CT全身检查" class="w-24 h-24 rounded-2xl object-cover">
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-crown text-white text-xs"></i>
                        </div>
                    </div>
                    <div class="ml-5 flex-1">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h4 class="font-bold text-gray-800 text-lg">PET-CT全身精密检查</h4>
                                <div class="flex items-center mt-1">
                                    <span class="certification-badge text-xs">
                                        <i class="fas fa-medal"></i>
                                        旗舰套餐
                                    </span>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="price-tag text-lg">¥28,800</span>
                                <div class="text-xs text-gray-500 line-through mt-1">¥32,000</div>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-3 leading-relaxed">早期癌症筛查 · 全身MRI · 心脏检查 · 专家解读</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-xs text-gray-500">
                                <i class="fas fa-map-marker-alt mr-1 text-blue-500"></i>
                                <span>东京大学医院 · 2天1夜</span>
                            </div>
                            <div class="flex items-center text-xs">
                                <div class="flex text-yellow-400 mr-1">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="text-gray-600">4.9 (128评价)</span>
                            </div>
                        </div>
                        <div class="mt-3 flex items-center justify-between">
                            <div class="flex items-center text-xs text-green-600">
                                <i class="fas fa-check-circle mr-1"></i>
                                <span>本月已预约 89 人</span>
                            </div>
                            <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors">
                                立即预约
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="package-card p-6 relative overflow-hidden">
                <div class="absolute top-4 right-4">
                    <span class="bg-pink-500 text-white text-xs px-2 py-1 rounded-full">推荐</span>
                </div>
                <div class="flex">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=100&h=100&fit=crop"
                             alt="高端女性体检" class="w-24 h-24 rounded-2xl object-cover">
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-heart text-white text-xs"></i>
                        </div>
                    </div>
                    <div class="ml-5 flex-1">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h4 class="font-bold text-gray-800 text-lg">高端女性专项体检</h4>
                                <div class="flex items-center mt-1">
                                    <span class="bg-pink-100 text-pink-600 text-xs px-2 py-1 rounded-full font-medium">
                                        <i class="fas fa-female"></i>
                                        女性专享
                                    </span>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="price-tag text-lg" style="background: linear-gradient(135deg, #f093fb, #f5576c);">¥18,800</span>
                                <div class="text-xs text-gray-500 line-through mt-1">¥21,000</div>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-3 leading-relaxed">乳腺癌筛查 · 妇科检查 · 骨密度 · 激素检测</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-xs text-gray-500">
                                <i class="fas fa-map-marker-alt mr-1 text-pink-500"></i>
                                <span>大阪妇女医院 · 1天</span>
                            </div>
                            <div class="flex items-center text-xs">
                                <div class="flex text-yellow-400 mr-1">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="text-gray-600">4.8 (89评价)</span>
                            </div>
                        </div>
                        <div class="mt-3 flex items-center justify-between">
                            <div class="flex items-center text-xs text-green-600">
                                <i class="fas fa-check-circle mr-1"></i>
                                <span>本月已预约 56 人</span>
                            </div>
                            <button class="bg-pink-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-pink-600 transition-colors">
                                立即预约
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷服务 -->
    <div class="px-6 pb-8">
        <div class="flex items-center justify-between mb-5">
            <h3 class="text-xl font-bold text-gray-800">快捷服务</h3>
            <span class="text-sm text-gray-600">一键直达</span>
        </div>
        <div class="grid grid-cols-4 gap-4">
            <div class="text-center group">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:shadow-xl transition-all group-hover:scale-105">
                    <i class="fas fa-calendar-plus text-white text-xl"></i>
                </div>
                <span class="text-xs text-gray-700 font-medium">在线预约</span>
                <div class="w-1 h-1 bg-blue-500 rounded-full mx-auto mt-1 opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </div>
            <div class="text-center group">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:shadow-xl transition-all group-hover:scale-105">
                    <i class="fas fa-file-medical text-white text-xl"></i>
                </div>
                <span class="text-xs text-gray-700 font-medium">体检报告</span>
                <div class="w-1 h-1 bg-green-500 rounded-full mx-auto mt-1 opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </div>
            <div class="text-center group">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:shadow-xl transition-all group-hover:scale-105">
                    <i class="fas fa-video text-white text-xl"></i>
                </div>
                <span class="text-xs text-gray-700 font-medium">视频咨询</span>
                <div class="w-1 h-1 bg-purple-500 rounded-full mx-auto mt-1 opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </div>
            <div class="text-center group">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg group-hover:shadow-xl transition-all group-hover:scale-105">
                    <i class="fas fa-robot text-white text-xl"></i>
                </div>
                <span class="text-xs text-gray-700 font-medium">AI助手</span>
                <div class="w-1 h-1 bg-orange-500 rounded-full mx-auto mt-1 opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </div>
        </div>
    </div>

    <!-- 用户评价 -->
    <div class="px-6 pb-8">
        <h3 class="text-xl font-bold text-gray-800 mb-5">用户真实评价</h3>
        <div class="space-y-4">
            <div class="bg-white/80 backdrop-blur-lg rounded-2xl p-5 border border-white/20">
                <div class="flex items-center mb-3">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face"
                         alt="用户头像" class="w-10 h-10 rounded-full object-cover mr-3">
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h5 class="font-semibold text-gray-800">李女士</h5>
                            <div class="flex text-yellow-400 text-sm">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <p class="text-xs text-gray-600">PET-CT全身检查 · 2024年1月</p>
                    </div>
                </div>
                <p class="text-sm text-gray-700 leading-relaxed">"服务非常专业，医生很耐心，检查很全面。在东京的体检体验超出预期，发现了早期问题并及时治疗。强烈推荐！"</p>
            </div>

            <div class="bg-white/80 backdrop-blur-lg rounded-2xl p-5 border border-white/20">
                <div class="flex items-center mb-3">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face"
                         alt="用户头像" class="w-10 h-10 rounded-full object-cover mr-3">
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h5 class="font-semibold text-gray-800">张先生</h5>
                            <div class="flex text-yellow-400 text-sm">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <p class="text-xs text-gray-600">高端男性体检 · 2023年12月</p>
                    </div>
                </div>
                <p class="text-sm text-gray-700 leading-relaxed">"整个流程很顺畅，翻译服务很到位，医院环境一流。检查结果详细专业，物超所值的体验。"</p>
            </div>
        </div>
    </div>

    <script>
        // 高端轮播图控制
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const indicators = document.querySelectorAll('.absolute.bottom-6 div');

        function showSlide(index) {
            slides.forEach(slide => slide.classList.remove('active'));
            indicators.forEach(indicator => {
                indicator.classList.remove('opacity-100', 'w-8');
                indicator.classList.add('opacity-50', 'w-2');
            });

            slides[index].classList.add('active');
            indicators[index].classList.remove('opacity-50', 'w-2');
            indicators[index].classList.add('opacity-100', 'w-8');
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }

        // 轮播控制按钮事件
        document.querySelector('.banner-slider .absolute.right-4').addEventListener('click', nextSlide);
        document.querySelector('.banner-slider .absolute.left-4').addEventListener('click', prevSlide);

        // 自动轮播
        let autoSlideInterval = setInterval(nextSlide, 4000);

        // 鼠标悬停暂停自动轮播
        document.querySelector('.banner-slider').addEventListener('mouseenter', () => {
            clearInterval(autoSlideInterval);
        });

        document.querySelector('.banner-slider').addEventListener('mouseleave', () => {
            autoSlideInterval = setInterval(nextSlide, 4000);
        });

        // 页面加载动画
        window.addEventListener('load', function() {
            // 统计数字动画
            const statsNumbers = document.querySelectorAll('.stats-card .text-2xl');
            statsNumbers.forEach((number, index) => {
                const finalValue = number.textContent;
                number.textContent = '0';

                setTimeout(() => {
                    animateNumber(number, finalValue);
                }, index * 200);
            });
        });

        function animateNumber(element, finalValue) {
            const isPercentage = finalValue.includes('%');
            const hasPlus = finalValue.includes('+');
            const numericValue = parseInt(finalValue.replace(/[^\d]/g, ''));

            let current = 0;
            const increment = numericValue / 30;

            const timer = setInterval(() => {
                current += increment;
                if (current >= numericValue) {
                    current = numericValue;
                    clearInterval(timer);
                }

                let displayValue = Math.floor(current).toLocaleString();
                if (isPercentage) displayValue += '%';
                if (hasPlus) displayValue += '+';
                if (finalValue.includes('mm')) displayValue = Math.floor(current) + 'mm';

                element.textContent = displayValue;
            }, 50);
        }

        // 服务卡片悬停效果
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // 快捷服务点击效果
        document.querySelectorAll('.grid.grid-cols-4 .group').forEach(item => {
            item.addEventListener('click', function() {
                const icon = this.querySelector('i');
                icon.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    icon.style.transform = 'scale(1)';
                }, 200);
            });
        });
    </script>
</body>
</html>
