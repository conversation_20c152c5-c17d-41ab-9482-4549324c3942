<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .banner-slider {
            position: relative;
            overflow: hidden;
        }
        
        .slide {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }
        
        .slide.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .service-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 20px;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .service-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }
        
        .package-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .package-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        
        .price-tag {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部搜索栏 -->
    <div class="bg-white px-4 py-3 border-b border-gray-100">
        <div class="flex items-center bg-gray-100 rounded-full px-4 py-2">
            <i class="fas fa-search text-gray-400 mr-3"></i>
            <input type="text" placeholder="搜索体检项目..." class="bg-transparent flex-1 outline-none text-sm">
        </div>
    </div>

    <!-- 轮播图 -->
    <div class="banner-slider h-48 relative">
        <div class="slide active">
            <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=200&fit=crop" 
                 alt="日本精密体检" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                <div class="text-center text-white">
                    <h2 class="text-xl font-bold mb-2">日本精密体检</h2>
                    <p class="text-sm">早期癌症筛查 · PET-CT检测</p>
                </div>
            </div>
        </div>
        <div class="slide">
            <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=200&fit=crop" 
                 alt="高端医疗服务" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                <div class="text-center text-white">
                    <h2 class="text-xl font-bold mb-2">高端医疗服务</h2>
                    <p class="text-sm">专业医师团队 · 一对一服务</p>
                </div>
            </div>
        </div>
        <div class="slide">
            <img src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=400&h=200&fit=crop" 
                 alt="医疗旅游" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                <div class="text-center text-white">
                    <h2 class="text-xl font-bold mb-2">医疗旅游</h2>
                    <p class="text-sm">健康体检 · 日本之旅</p>
                </div>
            </div>
        </div>
        
        <!-- 轮播指示器 -->
        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            <div class="w-2 h-2 bg-white rounded-full opacity-100"></div>
            <div class="w-2 h-2 bg-white rounded-full opacity-50"></div>
            <div class="w-2 h-2 bg-white rounded-full opacity-50"></div>
        </div>
    </div>

    <!-- 服务亮点 -->
    <div class="px-4 py-6">
        <h3 class="text-lg font-bold text-gray-800 mb-4">为什么选择日本体检</h3>
        <div class="grid grid-cols-2 gap-4">
            <div class="service-card">
                <i class="fas fa-microscope text-2xl mb-3"></i>
                <h4 class="font-semibold mb-1">精密检测</h4>
                <p class="text-sm opacity-90">PET-CT可检测2mm肿瘤</p>
            </div>
            <div class="service-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <i class="fas fa-user-md text-2xl mb-3"></i>
                <h4 class="font-semibold mb-1">专业团队</h4>
                <p class="text-sm opacity-90">顶级医师一对一服务</p>
            </div>
            <div class="service-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <i class="fas fa-shield-alt text-2xl mb-3"></i>
                <h4 class="font-semibold mb-1">早期筛查</h4>
                <p class="text-sm opacity-90">300+种癌症筛查</p>
            </div>
            <div class="service-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <i class="fas fa-star text-2xl mb-3"></i>
                <h4 class="font-semibold mb-1">五星服务</h4>
                <p class="text-sm opacity-90">全程贴心陪护</p>
            </div>
        </div>
    </div>

    <!-- 热门套餐 -->
    <div class="px-4 pb-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800">热门体检套餐</h3>
            <span class="text-blue-500 text-sm">查看全部 ></span>
        </div>
        
        <div class="space-y-4">
            <div class="package-card p-4">
                <div class="flex">
                    <img src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=80&h=80&fit=crop" 
                         alt="PET-CT全身检查" class="w-20 h-20 rounded-lg object-cover">
                    <div class="ml-4 flex-1">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-semibold text-gray-800">PET-CT全身精密检查</h4>
                            <span class="price-tag">¥28,800</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">早期癌症筛查 · 全身MRI · 心脏检查</p>
                        <div class="flex items-center text-xs text-gray-500">
                            <i class="fas fa-map-marker-alt mr-1"></i>
                            <span>东京 · 2天1夜</span>
                            <span class="ml-4">⭐ 4.9 (128评价)</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="package-card p-4">
                <div class="flex">
                    <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=80&h=80&fit=crop" 
                         alt="高端女性体检" class="w-20 h-20 rounded-lg object-cover">
                    <div class="ml-4 flex-1">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-semibold text-gray-800">高端女性专项体检</h4>
                            <span class="price-tag">¥18,800</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">乳腺癌筛查 · 妇科检查 · 骨密度</p>
                        <div class="flex items-center text-xs text-gray-500">
                            <i class="fas fa-map-marker-alt mr-1"></i>
                            <span>大阪 · 1天</span>
                            <span class="ml-4">⭐ 4.8 (89评价)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷服务 -->
    <div class="px-4 pb-6">
        <h3 class="text-lg font-bold text-gray-800 mb-4">快捷服务</h3>
        <div class="grid grid-cols-4 gap-4">
            <div class="text-center">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-calendar-plus text-blue-500"></i>
                </div>
                <span class="text-xs text-gray-600">在线预约</span>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-file-medical text-green-500"></i>
                </div>
                <span class="text-xs text-gray-600">体检报告</span>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-comments text-purple-500"></i>
                </div>
                <span class="text-xs text-gray-600">专家咨询</span>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-headset text-orange-500"></i>
                </div>
                <span class="text-xs text-gray-600">客服支持</span>
            </div>
        </div>
    </div>

    <script>
        // 轮播图自动切换
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const indicators = document.querySelectorAll('.absolute.bottom-4 div');
        
        function showSlide(index) {
            slides.forEach(slide => slide.classList.remove('active'));
            indicators.forEach(indicator => indicator.classList.remove('opacity-100'));
            indicators.forEach(indicator => indicator.classList.add('opacity-50'));
            
            slides[index].classList.add('active');
            indicators[index].classList.remove('opacity-50');
            indicators[index].classList.add('opacity-100');
        }
        
        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }
        
        // 每3秒切换一次
        setInterval(nextSlide, 3000);
    </script>
</body>
</html>
