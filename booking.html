<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预约服务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .step-indicator {
            position: relative;
            transition: all 0.3s ease;
        }

        .step-indicator::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #e5e7eb, #f3f4f6);
            transform: translateY(-50%);
            border-radius: 2px;
        }

        .step-indicator.active::after {
            background: linear-gradient(90deg, #3b82f6, #667eea);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .step-indicator.completed::after {
            background: linear-gradient(90deg, #10b981, #059669);
        }

        .step-indicator:last-child::after {
            display: none;
        }

        .form-section {
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(20px);
        }

        .smart-recommendation {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(102, 126, 234, 0.05) 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .date-picker {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
            margin-top: 12px;
        }
        
        .date-item {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .date-item.available {
            background: #f3f4f6;
            color: #374151;
        }
        
        .date-item.available:hover {
            background: #e5e7eb;
        }
        
        .date-item.selected {
            background: #3b82f6;
            color: white;
        }
        
        .date-item.disabled {
            background: #f9fafb;
            color: #d1d5db;
            cursor: not-allowed;
        }
        
        .time-slot {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            text-center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .time-slot:hover {
            border-color: #3b82f6;
        }
        
        .time-slot.selected {
            border-color: #3b82f6;
            background: #eff6ff;
            color: #3b82f6;
        }
        
        .time-slot.disabled {
            background: #f9fafb;
            color: #d1d5db;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50">
    <!-- 顶部标题栏 -->
    <div class="bg-white/80 backdrop-blur-lg px-6 py-5 border-b border-white/20">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">预约体检</h1>
                <p class="text-sm text-gray-600 mt-1">智能预约 · 便捷高效</p>
            </div>
            <button class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                <i class="fas fa-question-circle text-gray-600"></i>
            </button>
        </div>
    </div>

    <!-- 步骤指示器 -->
    <div class="bg-white/80 backdrop-blur-lg px-6 py-5 border-b border-white/20">
        <div class="flex items-center justify-between">
            <div class="step-indicator active flex items-center">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                    <i class="fas fa-check"></i>
                </div>
                <span class="ml-3 text-sm font-semibold text-blue-600">选择套餐</span>
            </div>
            <div class="step-indicator flex items-center">
                <div class="w-10 h-10 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-bold">2</div>
                <span class="ml-3 text-sm text-gray-600">选择时间</span>
            </div>
            <div class="step-indicator flex items-center">
                <div class="w-10 h-10 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-bold">3</div>
                <span class="ml-3 text-sm text-gray-600">确认信息</span>
            </div>
            <div class="step-indicator flex items-center">
                <div class="w-10 h-10 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-bold">4</div>
                <span class="ml-3 text-sm text-gray-600">支付</span>
            </div>
        </div>
    </div>

    <div class="px-6 py-6">
        <!-- AI智能推荐 -->
        <div class="smart-recommendation mb-6">
            <div class="flex items-center mb-3">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                    <i class="fas fa-robot text-white text-sm"></i>
                </div>
                <h3 class="font-semibold text-blue-800">AI智能推荐</h3>
            </div>
            <p class="text-sm text-blue-700 mb-3">基于您的年龄、性别和健康状况，我们为您推荐以下套餐：</p>
            <div class="bg-white rounded-lg p-3">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-medium text-gray-800">PET-CT全身精密体检</h4>
                        <p class="text-xs text-gray-600">匹配度: 95%</p>
                    </div>
                    <button class="bg-blue-500 text-white px-3 py-1 rounded-lg text-xs">
                        选择
                    </button>
                </div>
            </div>
        </div>

        <!-- 选择套餐 -->
        <div class="form-section p-6" id="step1">
            <div class="flex items-center justify-between mb-5">
                <h3 class="text-xl font-bold text-gray-800">选择体检套餐</h3>
                <span class="text-sm text-gray-600">第1步 / 共4步</span>
            </div>

            <div class="space-y-4">
                <div class="border-2 border-blue-500 bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl p-5 cursor-pointer relative overflow-hidden">
                    <div class="absolute top-3 right-3">
                        <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">推荐</span>
                    </div>
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <h4 class="font-bold text-gray-800 text-lg">PET-CT全身精密体检</h4>
                                <span class="ml-2 bg-yellow-400 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium">Premium</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-3 leading-relaxed">早期癌症筛查 · 全身MRI · 心脏检查 · 专家解读</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-xs text-gray-500">
                                    <i class="fas fa-map-marker-alt mr-1 text-blue-500"></i>
                                    <span>东京大学医院 · 2天1夜</span>
                                </div>
                                <div class="flex items-center text-xs">
                                    <div class="flex text-yellow-400 mr-1">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="text-gray-600">4.9分</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right ml-4">
                            <div class="text-2xl font-bold text-blue-600">¥28,800</div>
                            <div class="text-sm text-gray-500 line-through">¥32,000</div>
                            <div class="text-xs text-green-600 font-medium">节省¥3,200</div>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center justify-between">
                        <div class="flex items-center text-blue-600">
                            <i class="fas fa-check-circle mr-2 text-lg"></i>
                            <span class="text-sm font-semibold">已选择</span>
                        </div>
                        <div class="flex items-center text-xs text-green-600">
                            <i class="fas fa-users mr-1"></i>
                            <span>本月已有89人预约</span>
                        </div>
                    </div>
                </div>

                <div class="border-2 border-gray-200 rounded-2xl p-5 cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <h4 class="font-bold text-gray-800 text-lg">高端女性专项体检</h4>
                                <span class="ml-2 bg-pink-100 text-pink-600 text-xs px-2 py-1 rounded-full font-medium">女性专享</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-3 leading-relaxed">乳腺癌筛查 · 妇科检查 · 骨密度 · 激素检测</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-xs text-gray-500">
                                    <i class="fas fa-map-marker-alt mr-1 text-pink-500"></i>
                                    <span>大阪妇女医院 · 1天</span>
                                </div>
                                <div class="flex items-center text-xs">
                                    <div class="flex text-yellow-400 mr-1">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="text-gray-600">4.8分</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right ml-4">
                            <div class="text-2xl font-bold text-gray-800">¥18,800</div>
                            <div class="text-sm text-gray-500 line-through">¥21,000</div>
                            <div class="text-xs text-green-600 font-medium">节省¥2,200</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 flex gap-3">
                <button class="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all" onclick="nextStep(2)">
                    下一步：选择时间
                </button>
                <button class="px-6 bg-gray-100 text-gray-700 py-4 rounded-xl font-medium hover:bg-gray-200 transition-all">
                    对比套餐
                </button>
            </div>
        </div>

        <!-- 选择时间 -->
        <div class="form-section p-4 hidden" id="step2">
            <h3 class="text-lg font-bold text-gray-800 mb-4">选择体检时间</h3>
            
            <!-- 日期选择 -->
            <div class="mb-6">
                <h4 class="font-medium text-gray-800 mb-2">选择日期</h4>
                <div class="text-center mb-2">
                    <span class="text-lg font-semibold">2024年1月</span>
                </div>
                <div class="date-picker">
                    <div class="text-center text-xs text-gray-500 font-medium">日</div>
                    <div class="text-center text-xs text-gray-500 font-medium">一</div>
                    <div class="text-center text-xs text-gray-500 font-medium">二</div>
                    <div class="text-center text-xs text-gray-500 font-medium">三</div>
                    <div class="text-center text-xs text-gray-500 font-medium">四</div>
                    <div class="text-center text-xs text-gray-500 font-medium">五</div>
                    <div class="text-center text-xs text-gray-500 font-medium">六</div>
                    
                    <div class="date-item disabled">31</div>
                    <div class="date-item available">1</div>
                    <div class="date-item available">2</div>
                    <div class="date-item available">3</div>
                    <div class="date-item available">4</div>
                    <div class="date-item available">5</div>
                    <div class="date-item available">6</div>
                    
                    <div class="date-item available">7</div>
                    <div class="date-item available">8</div>
                    <div class="date-item available">9</div>
                    <div class="date-item available">10</div>
                    <div class="date-item available">11</div>
                    <div class="date-item selected">12</div>
                    <div class="date-item available">13</div>
                    
                    <div class="date-item available">14</div>
                    <div class="date-item available">15</div>
                    <div class="date-item available">16</div>
                    <div class="date-item available">17</div>
                    <div class="date-item available">18</div>
                    <div class="date-item available">19</div>
                    <div class="date-item available">20</div>
                </div>
            </div>
            
            <!-- 时间段选择 -->
            <div class="mb-6">
                <h4 class="font-medium text-gray-800 mb-3">选择时间段</h4>
                <div class="grid grid-cols-2 gap-3">
                    <div class="time-slot">
                        <div class="font-medium">上午</div>
                        <div class="text-sm text-gray-600">08:00-12:00</div>
                    </div>
                    <div class="time-slot selected">
                        <div class="font-medium">下午</div>
                        <div class="text-sm text-gray-600">13:00-17:00</div>
                    </div>
                </div>
            </div>
            
            <div class="flex gap-3">
                <button class="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-medium" onclick="prevStep(1)">
                    上一步
                </button>
                <button class="flex-1 bg-blue-500 text-white py-3 rounded-lg font-medium" onclick="nextStep(3)">
                    下一步
                </button>
            </div>
        </div>

        <!-- 确认信息 -->
        <div class="form-section p-4 hidden" id="step3">
            <h3 class="text-lg font-bold text-gray-800 mb-4">填写个人信息</h3>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">姓名 *</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入您的姓名">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">手机号 *</label>
                    <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入手机号">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">身份证号 *</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入身份证号">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">性别 *</label>
                    <div class="flex gap-4">
                        <label class="flex items-center">
                            <input type="radio" name="gender" value="male" class="mr-2">
                            <span>男</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="gender" value="female" class="mr-2">
                            <span>女</span>
                        </label>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">出生日期 *</label>
                    <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">特殊需求</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="请描述您的特殊需求或健康状况"></textarea>
                </div>
            </div>
            
            <!-- 预约摘要 -->
            <div class="bg-gray-50 rounded-lg p-4 mt-6">
                <h4 class="font-medium text-gray-800 mb-3">预约摘要</h4>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">体检套餐：</span>
                        <span class="font-medium">PET-CT全身精密体检</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">体检时间：</span>
                        <span class="font-medium">2024年1月12日 下午</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">体检地点：</span>
                        <span class="font-medium">东京都</span>
                    </div>
                    <div class="flex justify-between border-t pt-2 mt-2">
                        <span class="text-gray-600">费用：</span>
                        <span class="font-bold text-lg text-blue-600">¥28,800</span>
                    </div>
                </div>
            </div>
            
            <div class="flex gap-3 mt-6">
                <button class="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-medium" onclick="prevStep(2)">
                    上一步
                </button>
                <button class="flex-1 bg-blue-500 text-white py-3 rounded-lg font-medium" onclick="submitBooking()">
                    确认预约
                </button>
            </div>
        </div>

        <!-- 支付步骤 -->
        <div class="form-section p-6 hidden" id="step4">
            <div class="flex items-center justify-between mb-5">
                <h3 class="text-xl font-bold text-gray-800">支付确认</h3>
                <span class="text-sm text-gray-600">第4步 / 共4步</span>
            </div>

            <!-- 订单摘要 -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-5 mb-6">
                <h4 class="font-semibold text-gray-800 mb-4">订单摘要</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">体检套餐：</span>
                        <span class="font-medium">PET-CT全身精密体检</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">体检时间：</span>
                        <span class="font-medium">2024年1月12日 下午</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">体检地点：</span>
                        <span class="font-medium">东京大学医院</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">预约人：</span>
                        <span class="font-medium">张先生</span>
                    </div>
                    <hr class="border-gray-200">
                    <div class="flex justify-between items-center text-lg">
                        <span class="text-gray-600">总费用：</span>
                        <span class="font-bold text-blue-600">¥28,800</span>
                    </div>
                </div>
            </div>

            <!-- 支付方式 -->
            <div class="mb-6">
                <h4 class="font-semibold text-gray-800 mb-4">选择支付方式</h4>
                <div class="space-y-3">
                    <div class="border-2 border-blue-500 bg-blue-50 rounded-xl p-4 cursor-pointer">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4">
                                <i class="fab fa-weixin text-white text-xl"></i>
                            </div>
                            <div class="flex-1">
                                <h5 class="font-medium text-gray-800">微信支付</h5>
                                <p class="text-sm text-gray-600">安全便捷，支持分期</p>
                            </div>
                            <i class="fas fa-check-circle text-blue-500 text-xl"></i>
                        </div>
                    </div>

                    <div class="border-2 border-gray-200 rounded-xl p-4 cursor-pointer hover:border-gray-300">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                                <i class="fab fa-alipay text-white text-xl"></i>
                            </div>
                            <div class="flex-1">
                                <h5 class="font-medium text-gray-800">支付宝</h5>
                                <p class="text-sm text-gray-600">花呗分期，免息优惠</p>
                            </div>
                        </div>
                    </div>

                    <div class="border-2 border-gray-200 rounded-xl p-4 cursor-pointer hover:border-gray-300">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gray-600 rounded-xl flex items-center justify-center mr-4">
                                <i class="fas fa-credit-card text-white text-xl"></i>
                            </div>
                            <div class="flex-1">
                                <h5 class="font-medium text-gray-800">银行卡支付</h5>
                                <p class="text-sm text-gray-600">支持各大银行卡</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 优惠券 -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-ticket-alt text-yellow-600 mr-3"></i>
                        <div>
                            <h5 class="font-medium text-gray-800">优惠券</h5>
                            <p class="text-sm text-gray-600">可用优惠券 2 张</p>
                        </div>
                    </div>
                    <button class="text-blue-500 text-sm font-medium">
                        选择优惠券 >
                    </button>
                </div>
            </div>

            <div class="flex gap-3">
                <button class="flex-1 bg-gray-200 text-gray-700 py-4 rounded-xl font-semibold" onclick="prevStep(3)">
                    上一步
                </button>
                <button class="flex-1 bg-gradient-to-r from-green-500 to-green-600 text-white py-4 rounded-xl font-semibold text-lg shadow-lg" onclick="processPayment()">
                    确认支付 ¥28,800
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;

        function nextStep(step) {
            // 隐藏当前步骤
            document.querySelectorAll('[id^="step"]').forEach(el => el.classList.add('hidden'));

            // 显示目标步骤
            document.getElementById('step' + step).classList.remove('hidden');

            // 更新步骤指示器
            updateStepIndicator(step);
            currentStep = step;
        }
        
        function prevStep(step) {
            nextStep(step);
        }
        
        function updateStepIndicator(currentStep) {
            const indicators = document.querySelectorAll('.step-indicator');

            indicators.forEach((indicator, index) => {
                const stepNumber = index + 1;
                const circle = indicator.querySelector('div');
                const text = indicator.querySelector('span');

                // 重置所有样式
                circle.classList.remove('bg-gray-300', 'text-gray-600', 'bg-blue-500', 'bg-gradient-to-r', 'from-blue-500', 'to-blue-600', 'bg-green-500');
                text.classList.remove('text-gray-600', 'text-blue-600', 'text-green-600');
                indicator.classList.remove('active', 'completed');

                if (stepNumber < currentStep) {
                    // 已完成的步骤
                    circle.classList.add('bg-green-500', 'text-white');
                    circle.innerHTML = '<i class="fas fa-check text-sm"></i>';
                    text.classList.add('text-green-600', 'font-semibold');
                    indicator.classList.add('completed');
                } else if (stepNumber === currentStep) {
                    // 当前步骤
                    circle.classList.add('bg-gradient-to-r', 'from-blue-500', 'to-blue-600', 'text-white');
                    circle.innerHTML = stepNumber;
                    text.classList.add('text-blue-600', 'font-semibold');
                    indicator.classList.add('active');
                } else {
                    // 未完成的步骤
                    circle.classList.add('bg-gray-300', 'text-gray-600');
                    circle.innerHTML = stepNumber;
                    text.classList.add('text-gray-600');
                }
            });
        }
        
        function submitBooking() {
            // 显示支付步骤
            nextStep(4);
        }

        function processPayment() {
            // 模拟支付处理
            const button = event.target;
            const originalText = button.textContent;

            button.disabled = true;
            button.textContent = '处理中...';
            button.classList.add('opacity-50');

            setTimeout(() => {
                // 显示支付成功
                showPaymentSuccess();
            }, 2000);
        }

        function showPaymentSuccess() {
            // 创建支付成功页面
            const container = document.querySelector('.px-6.py-6');
            container.innerHTML = `
                <div class="text-center py-12">
                    <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-check text-white text-3xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-3">支付成功！</h2>
                    <p class="text-gray-600 mb-6">您的体检预约已确认，我们将在24小时内联系您</p>

                    <div class="bg-gray-50 rounded-2xl p-5 mb-6 text-left">
                        <h3 class="font-semibold mb-3">预约信息</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">订单号：</span>
                                <span class="font-medium">TJ202401120001</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">体检套餐：</span>
                                <span class="font-medium">PET-CT全身精密体检</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">体检时间：</span>
                                <span class="font-medium">2024年1月12日 下午</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">支付金额：</span>
                                <span class="font-medium text-green-600">¥28,800</span>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <button class="w-full bg-blue-500 text-white py-3 rounded-xl font-medium">
                            查看订单详情
                        </button>
                        <button class="w-full bg-gray-100 text-gray-700 py-3 rounded-xl font-medium">
                            返回首页
                        </button>
                    </div>
                </div>
            `;
        }
        
        // 日期选择交互
        document.querySelectorAll('.date-item.available').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.date-item').forEach(d => d.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
        
        // 时间段选择交互
        document.querySelectorAll('.time-slot:not(.disabled)').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.time-slot').forEach(t => t.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
    </script>
</body>
</html>
