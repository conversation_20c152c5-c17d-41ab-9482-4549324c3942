<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预约服务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .step-indicator {
            position: relative;
        }
        
        .step-indicator::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 100%;
            height: 2px;
            background: #e5e7eb;
            transform: translateY(-50%);
        }
        
        .step-indicator.active::after {
            background: #3b82f6;
        }
        
        .step-indicator:last-child::after {
            display: none;
        }
        
        .form-section {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            margin-bottom: 16px;
        }
        
        .date-picker {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
            margin-top: 12px;
        }
        
        .date-item {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .date-item.available {
            background: #f3f4f6;
            color: #374151;
        }
        
        .date-item.available:hover {
            background: #e5e7eb;
        }
        
        .date-item.selected {
            background: #3b82f6;
            color: white;
        }
        
        .date-item.disabled {
            background: #f9fafb;
            color: #d1d5db;
            cursor: not-allowed;
        }
        
        .time-slot {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            text-center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .time-slot:hover {
            border-color: #3b82f6;
        }
        
        .time-slot.selected {
            border-color: #3b82f6;
            background: #eff6ff;
            color: #3b82f6;
        }
        
        .time-slot.disabled {
            background: #f9fafb;
            color: #d1d5db;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部标题栏 -->
    <div class="bg-white px-4 py-4 border-b border-gray-100">
        <h1 class="text-xl font-bold text-gray-800 text-center">预约体检</h1>
    </div>

    <!-- 步骤指示器 -->
    <div class="bg-white px-4 py-4 border-b border-gray-100">
        <div class="flex items-center justify-between">
            <div class="step-indicator active flex items-center">
                <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                <span class="ml-2 text-sm font-medium text-blue-500">选择套餐</span>
            </div>
            <div class="step-indicator flex items-center">
                <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                <span class="ml-2 text-sm text-gray-600">选择时间</span>
            </div>
            <div class="step-indicator flex items-center">
                <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                <span class="ml-2 text-sm text-gray-600">确认信息</span>
            </div>
        </div>
    </div>

    <div class="px-4 py-4">
        <!-- 选择套餐 -->
        <div class="form-section p-4" id="step1">
            <h3 class="text-lg font-bold text-gray-800 mb-4">选择体检套餐</h3>
            
            <div class="space-y-3">
                <div class="border-2 border-blue-500 bg-blue-50 rounded-lg p-4 cursor-pointer">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-800">PET-CT全身精密体检</h4>
                            <p class="text-sm text-gray-600 mt-1">早期癌症筛查 · 全身MRI · 心脏检查</p>
                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                <span>东京都 · 2天1夜</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-xl font-bold text-blue-600">¥28,800</div>
                            <div class="text-xs text-gray-500 line-through">¥32,000</div>
                        </div>
                    </div>
                    <div class="mt-3 flex items-center text-blue-600">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span class="text-sm font-medium">已选择</span>
                    </div>
                </div>
                
                <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-gray-300">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-800">高端女性专项体检</h4>
                            <p class="text-sm text-gray-600 mt-1">乳腺癌筛查 · 妇科检查 · 骨密度</p>
                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                <span>大阪 · 1天</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-xl font-bold text-gray-800">¥18,800</div>
                            <div class="text-xs text-gray-500 line-through">¥21,000</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <button class="w-full bg-blue-500 text-white py-3 rounded-lg font-medium mt-4" onclick="nextStep(2)">
                下一步：选择时间
            </button>
        </div>

        <!-- 选择时间 -->
        <div class="form-section p-4 hidden" id="step2">
            <h3 class="text-lg font-bold text-gray-800 mb-4">选择体检时间</h3>
            
            <!-- 日期选择 -->
            <div class="mb-6">
                <h4 class="font-medium text-gray-800 mb-2">选择日期</h4>
                <div class="text-center mb-2">
                    <span class="text-lg font-semibold">2024年1月</span>
                </div>
                <div class="date-picker">
                    <div class="text-center text-xs text-gray-500 font-medium">日</div>
                    <div class="text-center text-xs text-gray-500 font-medium">一</div>
                    <div class="text-center text-xs text-gray-500 font-medium">二</div>
                    <div class="text-center text-xs text-gray-500 font-medium">三</div>
                    <div class="text-center text-xs text-gray-500 font-medium">四</div>
                    <div class="text-center text-xs text-gray-500 font-medium">五</div>
                    <div class="text-center text-xs text-gray-500 font-medium">六</div>
                    
                    <div class="date-item disabled">31</div>
                    <div class="date-item available">1</div>
                    <div class="date-item available">2</div>
                    <div class="date-item available">3</div>
                    <div class="date-item available">4</div>
                    <div class="date-item available">5</div>
                    <div class="date-item available">6</div>
                    
                    <div class="date-item available">7</div>
                    <div class="date-item available">8</div>
                    <div class="date-item available">9</div>
                    <div class="date-item available">10</div>
                    <div class="date-item available">11</div>
                    <div class="date-item selected">12</div>
                    <div class="date-item available">13</div>
                    
                    <div class="date-item available">14</div>
                    <div class="date-item available">15</div>
                    <div class="date-item available">16</div>
                    <div class="date-item available">17</div>
                    <div class="date-item available">18</div>
                    <div class="date-item available">19</div>
                    <div class="date-item available">20</div>
                </div>
            </div>
            
            <!-- 时间段选择 -->
            <div class="mb-6">
                <h4 class="font-medium text-gray-800 mb-3">选择时间段</h4>
                <div class="grid grid-cols-2 gap-3">
                    <div class="time-slot">
                        <div class="font-medium">上午</div>
                        <div class="text-sm text-gray-600">08:00-12:00</div>
                    </div>
                    <div class="time-slot selected">
                        <div class="font-medium">下午</div>
                        <div class="text-sm text-gray-600">13:00-17:00</div>
                    </div>
                </div>
            </div>
            
            <div class="flex gap-3">
                <button class="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-medium" onclick="prevStep(1)">
                    上一步
                </button>
                <button class="flex-1 bg-blue-500 text-white py-3 rounded-lg font-medium" onclick="nextStep(3)">
                    下一步
                </button>
            </div>
        </div>

        <!-- 确认信息 -->
        <div class="form-section p-4 hidden" id="step3">
            <h3 class="text-lg font-bold text-gray-800 mb-4">填写个人信息</h3>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">姓名 *</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入您的姓名">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">手机号 *</label>
                    <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入手机号">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">身份证号 *</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入身份证号">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">性别 *</label>
                    <div class="flex gap-4">
                        <label class="flex items-center">
                            <input type="radio" name="gender" value="male" class="mr-2">
                            <span>男</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="gender" value="female" class="mr-2">
                            <span>女</span>
                        </label>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">出生日期 *</label>
                    <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">特殊需求</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="请描述您的特殊需求或健康状况"></textarea>
                </div>
            </div>
            
            <!-- 预约摘要 -->
            <div class="bg-gray-50 rounded-lg p-4 mt-6">
                <h4 class="font-medium text-gray-800 mb-3">预约摘要</h4>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">体检套餐：</span>
                        <span class="font-medium">PET-CT全身精密体检</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">体检时间：</span>
                        <span class="font-medium">2024年1月12日 下午</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">体检地点：</span>
                        <span class="font-medium">东京都</span>
                    </div>
                    <div class="flex justify-between border-t pt-2 mt-2">
                        <span class="text-gray-600">费用：</span>
                        <span class="font-bold text-lg text-blue-600">¥28,800</span>
                    </div>
                </div>
            </div>
            
            <div class="flex gap-3 mt-6">
                <button class="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-medium" onclick="prevStep(2)">
                    上一步
                </button>
                <button class="flex-1 bg-blue-500 text-white py-3 rounded-lg font-medium" onclick="submitBooking()">
                    确认预约
                </button>
            </div>
        </div>
    </div>

    <script>
        function nextStep(step) {
            // 隐藏当前步骤
            document.querySelectorAll('[id^="step"]').forEach(el => el.classList.add('hidden'));
            
            // 显示目标步骤
            document.getElementById('step' + step).classList.remove('hidden');
            
            // 更新步骤指示器
            updateStepIndicator(step);
        }
        
        function prevStep(step) {
            nextStep(step);
        }
        
        function updateStepIndicator(currentStep) {
            const indicators = document.querySelectorAll('.step-indicator');
            
            indicators.forEach((indicator, index) => {
                const stepNumber = index + 1;
                const circle = indicator.querySelector('div');
                const text = indicator.querySelector('span');
                
                if (stepNumber <= currentStep) {
                    circle.classList.remove('bg-gray-300', 'text-gray-600');
                    circle.classList.add('bg-blue-500', 'text-white');
                    text.classList.remove('text-gray-600');
                    text.classList.add('text-blue-500');
                    indicator.classList.add('active');
                } else {
                    circle.classList.remove('bg-blue-500', 'text-white');
                    circle.classList.add('bg-gray-300', 'text-gray-600');
                    text.classList.remove('text-blue-500');
                    text.classList.add('text-gray-600');
                    indicator.classList.remove('active');
                }
            });
        }
        
        function submitBooking() {
            alert('预约提交成功！我们将在24小时内联系您确认详细信息。');
        }
        
        // 日期选择交互
        document.querySelectorAll('.date-item.available').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.date-item').forEach(d => d.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
        
        // 时间段选择交互
        document.querySelectorAll('.time-slot:not(.disabled)').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.time-slot').forEach(t => t.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
    </script>
</body>
</html>
